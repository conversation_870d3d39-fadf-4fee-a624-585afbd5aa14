<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Test</title>
</head>
<body>
    <h1>JavaScript Test Page</h1>
    
    <button onclick="testBasicJS()">Test Basic JavaScript</button>
    <button onclick="testFileUpload()">Test File Upload</button>
    
    <div id="output"></div>
    
    <script>
        console.log('🚀 Test JavaScript loaded');
        
        function testBasicJS() {
            console.log('✅ Basic JavaScript works');
            document.getElementById('output').innerHTML = '<p style="color: green;">✅ Basic JavaScript works!</p>';
            alert('JavaScript is working!');
        }
        
        function testFileUpload() {
            console.log('🧪 Testing file upload');
            
            // Create file input
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.csv';
            input.onchange = function(e) {
                console.log('📁 File selected:', e.target.files.length);
                document.getElementById('output').innerHTML = `<p style="color: blue;">📁 File selected: ${e.target.files[0]?.name || 'None'}</p>`;
            };
            
            // Trigger file dialog
            input.click();
        }
        
        // Test on page load
        document.addEventListener('DOMContentLoaded', () => {
            console.log('📄 Test page loaded');
            document.getElementById('output').innerHTML = '<p style="color: orange;">📄 Page loaded, JavaScript working</p>';
        });
    </script>
</body>
</html>
