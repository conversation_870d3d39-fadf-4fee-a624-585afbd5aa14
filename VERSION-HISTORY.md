# IOSS Reporter - Version History

## Version 2.1 (2025-06-17) ✅ CURRENT BACKUP

### 🎯 Critical Fix Added:

#### **Tax Name Detection for Taxes = 0 Scenarios**
- **Problem**: When `Subtotal + Shipping = Total` and `Taxes = 0`, system incorrectly treated all scenarios as NET values
- **Solution**: Added Tax Name field detection to distinguish between NET and GROSS pricing
- **Impact**: Ensures accurate VAT calculations for both pricing structures

#### **Two Distinct Scenarios Now Handled:**

**Scenario 1: NET Values (Tax Name Exists)**
```
Subtotal + Shipping = Total
Taxes = 0
Tax 1 Name = "SI VAT 22%" (NOT empty)
→ Subtotal & Shipping are NET, calculate VAT
```

**Scenario 2: GROSS Values (Tax Name Empty)**
```
Subtotal + Shipping = Total
Taxes = 0
Tax 1 Name = "" (empty)
→ Subtotal & Shipping are GROSS, extract VAT
```

#### **Implementation Details:**
- **New Patterns**: `NET_ZERO_TAXES_WITH_TAX_NAME`, `GROSS_ZERO_TAXES_EMPTY_TAX_NAME`
- **Multi-line Support**: Applied to both single and multi-item transactions
- **Shipping VAT**: Proper handling for both NET and GROSS scenarios
- **Tax Name Fields**: Checks `Tax 1 Name`, `Tax Name`, `Tax 1 Title`

---

## Version 2.0 (2025-06-17) ✅ PREVIOUS BACKUP

### 🎯 Major Features Added:

#### 1. **Fixed VAT Rates for Shopify IOSS Data**
- **Problem**: Shopify IOSS Data was using implied VAT rates (Taxes/Subtotal * 100) resulting in invalid rates like 20.0%, 22.3%, 28.7%
- **Solution**: Now uses valid country-specific VAT rates (e.g., Austria: 10%, 13%, 20%)
- **Impact**: Ensures IOSS compliance with proper VAT rate reporting

#### 2. **Calculate Net Value from VAT Amount**
- **Formula**: `Net Value = VAT Amount / (VAT Rate / 100)`
- **Benefit**: More accurate net values derived from VAT amounts rather than Shopify's subtotal
- **Example**: €20 VAT @ 20% = €100 Net Value (instead of using Shopify's potentially inaccurate subtotal)

#### 3. **IOSS Compliant Export Formats**
- **CSV Format**: Updated to `CountryCode,SupplyType,VATRateType,VATRate,TaxableAmount,VATAmount,Currency,ReportingPeriod`
- **XML Format**: Updated to Estonian IOSS format with proper schema structure
- **VAT Rate Types**: Proper categorization (STANDARD, REDUCED, SUPER_REDUCED, PARKING, ZERO)

#### 4. **GR to EL Country Code Mapping**
- **Requirement**: IOSS compliance requires Greece to be reported as "EL" not "GR"
- **Implementation**: Added `mapToIOSSCountryCode()` function
- **Coverage**: Applied to all CSV and XML exports (Shopify and main IOSS)

#### 5. **Fixed Country Count Alignment**
- **Problem**: Main calculation showed 28 countries, Shopify IOSS Data showed 24 countries
- **Root Cause**: Shopify IOSS Data had overly restrictive filtering (required postal codes, excluded "invalid" VAT rates)
- **Solution**: Aligned filtering logic to match main calculation
- **Result**: Both now show 28 countries consistently

#### 6. **Enhanced VAT Rate Validation**
- **Before**: Invalid VAT rates caused orders to be excluded
- **After**: Invalid VAT rates generate warnings but orders are included
- **Benefit**: Better coverage while maintaining data quality awareness

#### 7. **Removed Postal Code Requirement**
- **Problem**: Some valid EU orders were excluded due to missing postal codes
- **Solution**: Made postal codes optional (same as main calculation)
- **Impact**: Improved country coverage from 24 to 28 countries

### 🔧 Technical Improvements:

- **Centralized Functions**: `mapToIOSSCountryCode()`, `isValidVATRateForCountry()`, `getValidVATRatesForCountry()`
- **Consistent Filtering**: Shopify IOSS Data now uses same logic as main calculation
- **Better Logging**: VAT rate adjustments and net value calculations are logged
- **File Naming**: Updated to `shopify-ioss-compliant.csv` and `shopify-ioss-estonian.xml`

### 📊 Expected Results:

1. **Valid VAT Rates Only**: Austria shows 10%, 13%, 20% (not 20.0%, 22.3%, etc.)
2. **Accurate Net Values**: Calculated from VAT amounts using valid rates
3. **IOSS Compliant Exports**: Proper format for EU tax authority submission
4. **Greece Mapping**: GR → EL in all exports
5. **Country Alignment**: 28 countries in both main and Shopify calculations
6. **Better Coverage**: More orders included due to relaxed filtering

### 🚀 Files Changed:

- `script.js` - Main application with all v2.0 features
- `script-v2.0-backup.js` - Complete backup of v2.0 state

---

## Version 1.0 (Previous)

- Basic IOSS calculation functionality
- Shopify CSV processing
- Exchange rate management
- Basic export formats
- Initial VAT rate detection

---

## Backup Strategy

- **v2.1 Backup**: `script-v2.1-backup.js` (Latest with Tax Name detection fix)
- **v2.0 Backup**: `script-v2.0-backup.js` (Previous working version)
- **Version History**: This file documents all changes
- **Restore Process**: Copy backup file over `script.js` to restore specific version

---

## Next Steps

Any future changes should:
1. Create a new backup before modifications
2. Update this version history
3. Test thoroughly before deployment
4. Maintain backward compatibility where possible
