/**
 * IOSS Reporter - Shopify Order Processing for IOSS Compliance
 * Version: 2.0 (Recreated with enhanced backup safety)
 * 
 * This script processes Shopify order data and refunds for IOSS (Import One-Stop Shop) compliance.
 * Features automatic backup creation before any file operations.
 */

class IOSSReporter {
    constructor() {
        // Data storage
        this.csvData = [];
        this.refundsData = [];
        this.processedData = {};
        this.companyInfo = {};
        this.issues = [];
        
        // Exchange rates (fallback rates - in production these would be fetched from API)
        this.exchangeRates = {};
        this.fallbackRates = {
            'EUR': 1.0,    // Base currency
            'USD': 0.92,   // 1 USD = 0.92 EUR
            'GBP': 1.15,   // 1 GBP = 1.15 EUR
            'CAD': 0.67,   // 1 CAD = 0.67 EUR
            'AUD': 0.61,   // 1 AUD = 0.61 EUR
            'JPY': 0.0062, // 1 JPY = 0.0062 EUR
            'SEK': 0.087,  // 1 SEK = 0.087 EUR
            'NOK': 0.084,  // 1 NOK = 0.084 EUR
            'DKK': 0.134,  // 1 DKK = 0.134 EUR
            'CHF': 1.02,   // 1 CHF = 1.02 EUR
            'PLN': 0.23,   // 1 PLN = 0.23 EUR
            'CZK': 0.041   // 1 CZK = 0.041 EUR
        };
        
        // Configuration
        this.detectedCurrencies = new Set();
        this.userSpecifiedCurrency = null;
        this.iossEligibilityThreshold = 150; // EUR
        
        // EU country codes for IOSS eligibility
        this.euCountries = [
            'AT', 'BE', 'BG', 'HR', 'CY', 'CZ', 'DK', 'EE', 'FI', 'FR',
            'DE', 'GR', 'EL', 'HU', 'IE', 'IT', 'LV', 'LT', 'LU', 'MT', 
            'NL', 'PL', 'PT', 'RO', 'SK', 'SI', 'ES', 'SE'
        ];
        
        this.initializeApp();
    }

    initializeApp() {
        console.log('🚀 IOSS Reporter v2.0 initializing...');
        this.initializeEventListeners();
        this.populatePeriods();
        this.populateCurrencies();
        this.setupDragAndDrop();
        console.log('✅ IOSS Reporter initialized successfully');
    }

    initializeEventListeners() {
        // File upload handlers - Shopify Orders
        const fileInput = document.getElementById('file-input');
        const selectFileBtn = document.getElementById('select-file-btn');
        
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
        }
        if (selectFileBtn) {
            selectFileBtn.addEventListener('click', () => {
                if (fileInput) fileInput.click();
            });
        }
        
        // File upload handlers - Tax Reports (Refunds)
        const refundsFileInput = document.getElementById('refunds-file-input');
        const selectRefundsFileBtn = document.getElementById('select-refunds-file-btn');
        
        if (refundsFileInput) {
            refundsFileInput.addEventListener('change', (e) => this.handleRefundsFileUpload(e));
        }
        if (selectRefundsFileBtn) {
            selectRefundsFileBtn.addEventListener('click', () => {
                if (refundsFileInput) refundsFileInput.click();
            });
        }

        // Form submission
        const companyForm = document.getElementById('company-form');
        if (companyForm) {
            companyForm.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }
        
        // Process refunds button
        const processRefundsBtn = document.getElementById('process-refunds-btn');
        if (processRefundsBtn) {
            processRefundsBtn.addEventListener('click', () => this.processRefunds());
        }

        // Export buttons - Shopify Orders
        this.setupExportButton('export-csv', () => this.exportCSV());
        this.setupExportButton('export-ioss-compliant', () => this.exportIOSSCompliant());
        this.setupExportButton('export-detailed', () => this.exportDetailed());
        this.setupExportButton('export-xml', () => this.exportXML());
        this.setupExportButton('export-estonian-vat-xml', () => this.exportEstonianVATXML());

        // FIXED: Export buttons - Tax Reports (Refunds) - These were missing!
        this.setupExportButton('export-refunds-csv', () => this.exportRefundsCSV());
        this.setupExportButton('export-refunds-xml', () => this.exportRefundsXML());
        this.setupExportButton('export-refunds-detailed', () => this.exportRefundsDetailed());

        // Tab switching functionality
        window.switchTab = (tabName) => this.switchTab(tabName);
        
        console.log('✅ Event listeners initialized');
    }

    setupExportButton(buttonId, handler) {
        const button = document.getElementById(buttonId);
        if (button) {
            button.addEventListener('click', handler);
            console.log(`✅ Export button '${buttonId}' initialized`);
        } else {
            console.warn(`⚠️ Export button '${buttonId}' not found in DOM`);
        }
    }

    setupDragAndDrop() {
        const uploadArea = document.getElementById('upload-area');
        const refundsUploadArea = document.getElementById('refunds-upload-area');

        [uploadArea, refundsUploadArea].forEach(area => {
            if (!area) return;
            
            area.addEventListener('dragover', (e) => {
                e.preventDefault();
                area.classList.add('drag-over');
            });

            area.addEventListener('dragleave', () => {
                area.classList.remove('drag-over');
            });

            area.addEventListener('drop', (e) => {
                e.preventDefault();
                area.classList.remove('drag-over');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    if (area.id === 'upload-area') {
                        this.handleFileUpload({ target: { files } });
                    } else {
                        this.handleRefundsFileUpload({ target: { files: [files[0]] } });
                    }
                }
            });
        });
    }

    switchTab(tabName) {
        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        
        // Remove active class from all tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // Show selected tab content
        const tabContent = document.getElementById(`${tabName}-upload`);
        const tabButton = document.getElementById(`${tabName}-tab`);
        
        if (tabContent) tabContent.classList.add('active');
        if (tabButton) tabButton.classList.add('active');
    }

    populatePeriods() {
        const periodSelect = document.getElementById('reporting-period');
        if (!periodSelect) return;

        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth();

        // Generate periods for current and previous year
        for (let year = currentYear; year >= currentYear - 1; year--) {
            for (let month = 11; month >= 0; month--) {
                // Don't show future months for current year
                if (year === currentYear && month > currentMonth) continue;
                
                const date = new Date(year, month);
                const monthName = date.toLocaleString('en', { month: 'long' });
                const value = `${year}-${String(month + 1).padStart(2, '0')}`;
                const option = document.createElement('option');
                option.value = value;
                option.textContent = `${monthName} ${year}`;
                periodSelect.appendChild(option);
            }
        }
    }

    populateCurrencies() {
        const select = document.getElementById('currency');
        if (!select) return;

        const currencies = Object.keys(this.fallbackRates).sort();

        currencies.forEach(currency => {
            const option = document.createElement('option');
            option.value = currency;
            option.textContent = `${currency} - ${this.getCurrencyName(currency)}`;
            select.appendChild(option);
        });
    }

    getCurrencyName(code) {
        const names = {
            'EUR': 'Euro',
            'USD': 'US Dollar',
            'GBP': 'British Pound',
            'CAD': 'Canadian Dollar',
            'AUD': 'Australian Dollar',
            'JPY': 'Japanese Yen',
            'SEK': 'Swedish Krona',
            'NOK': 'Norwegian Krone',
            'DKK': 'Danish Krone',
            'CHF': 'Swiss Franc',
            'PLN': 'Polish Zloty',
            'CZK': 'Czech Koruna'
        };
        return names[code] || code;
    }

    getCurrencySymbol(code) {
        const symbols = {
            'EUR': '€',
            'USD': '$',
            'GBP': '£',
            'JPY': '¥',
            'CHF': 'CHF',
            'SEK': 'kr',
            'NOK': 'kr',
            'DKK': 'kr',
            'PLN': 'zł',
            'CZK': 'Kč'
        };
        return symbols[code] || code;
    }

    // File handling methods
    async handleFileUpload(event) {
        const files = event.target.files;
        if (!files.length) return;

        this.csvData = [];
        this.issues = [];
        let totalRecords = 0;

        const filesList = document.getElementById('files-list');
        if (filesList) filesList.innerHTML = '';

        this.showProcessingStatus('Uploading and processing files...');

        for (const file of files) {
            if (!file.name.toLowerCase().endsWith('.csv')) {
                this.addIssue('error', `File ${file.name} is not a CSV file`);
                continue;
            }

            try {
                const text = await this.readFileAsText(file);
                const parsed = this.parseCSV(text);
                this.csvData.push(...parsed);
                totalRecords += parsed.length;

                // Add file info to display
                if (filesList) {
                    const fileInfo = document.createElement('div');
                    fileInfo.className = 'file-item';
                    fileInfo.innerHTML = `
                        <span class="file-name">${file.name}</span>
                        <span class="file-records">${parsed.length} records</span>
                    `;
                    filesList.appendChild(fileInfo);
                }

            } catch (error) {
                this.addIssue('error', `Error processing ${file.name}: ${error.message}`);
            }
        }

        this.updateElement('total-record-count', totalRecords);
        this.showElement('file-info');
        
        if (totalRecords > 0) {
            this.showElement('company-section');
        }

        this.hideProcessingStatus();
        console.log(`✅ Processed ${totalRecords} records from ${files.length} files`);
    }

    async handleRefundsFileUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        if (!file.name.toLowerCase().endsWith('.csv')) {
            this.addIssue('error', 'Please select a CSV file');
            return;
        }

        this.showRefundsProcessingStatus('Processing refunds file...');

        try {
            const text = await this.readFileAsText(file);
            const parsed = this.parseCSV(text);
            this.refundsData = parsed;

            // Count refunds (negative tax amounts)
            const refundCount = parsed.filter(row => {
                const taxAmount = parseFloat(row['Tax amount'] || row['Tax Amount'] || 0);
                return taxAmount < 0;
            }).length;

            this.updateElement('refunds-record-count', parsed.length);
            this.updateElement('refunds-count', refundCount);
            this.showElement('refunds-file-info');

            if (refundCount > 0) {
                this.showElement('refunds-process-section');
            }

            // Add file info
            const filesList = document.getElementById('refunds-files-list');
            if (filesList) {
                filesList.innerHTML = `
                    <div class="file-item">
                        <span class="file-name">${file.name}</span>
                        <span class="file-records">${parsed.length} records (${refundCount} refunds)</span>
                    </div>
                `;
            }

            this.hideRefundsProcessingStatus();
            console.log(`✅ Processed refunds file: ${parsed.length} records, ${refundCount} refunds`);

        } catch (error) {
            this.addIssue('error', `Error processing file: ${error.message}`);
            this.hideRefundsProcessingStatus();
        }
    }

    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = reject;
            reader.readAsText(file);
        });
    }

    parseCSV(text) {
        // Use Papa Parse if available, otherwise fallback to simple parsing
        if (typeof Papa !== 'undefined') {
            const result = Papa.parse(text, { 
                header: true, 
                skipEmptyLines: true,
                transformHeader: (header) => header.trim()
            });
            return result.data;
        } else {
            return this.simpleCSVParse(text);
        }
    }

    simpleCSVParse(text) {
        const lines = text.trim().split('\n');
        if (lines.length < 2) return [];

        const headers = this.parseCSVLine(lines[0]);
        const data = [];

        for (let i = 1; i < lines.length; i++) {
            const values = this.parseCSVLine(lines[i]);
            const row = {};
            headers.forEach((header, index) => {
                row[header.trim()] = values[index] ? values[index].trim() : '';
            });
            if (Object.values(row).some(val => val)) { // Skip empty rows
                data.push(row);
            }
        }

        return data;
    }

    parseCSVLine(line) {
        const result = [];
        let current = '';
        let inQuotes = false;
        
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            
            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                result.push(current);
                current = '';
            } else {
                current += char;
            }
        }
        
        result.push(current);
        return result.map(item => item.replace(/^"|"$/g, '')); // Remove surrounding quotes
    }

    async handleFormSubmit(event) {
        event.preventDefault();

        // CRITICAL FIX: Comprehensive data validation
        const companyName = this.getElementValue('company-name').trim();
        const iossNumber = this.getElementValue('ioss-number').trim();
        const period = this.getElementValue('reporting-period');
        const currency = this.getElementValue('currency');

        // Validate all required fields
        const validationErrors = this.validateCompanyData(companyName, iossNumber, period, currency);

        if (validationErrors.length > 0) {
            // Display validation errors
            validationErrors.forEach(error => this.addIssue('error', error));
            this.displayIssues();
            return;
        }

        this.companyInfo = {
            name: companyName,
            iossNumber: iossNumber,
            period: period,
            currency: currency
        };

        console.log('📋 Company info collected and validated:', this.companyInfo);
        await this.processOrderData();
    }

    // CRITICAL FIX: Comprehensive Data Validation
    validateCompanyData(companyName, iossNumber, period, currency) {
        const errors = [];

        // Validate company name
        if (!companyName || companyName.length < 2) {
            errors.push('Company name is required and must be at least 2 characters long');
        }

        // Validate IOSS number
        if (!this.isValidIOSSNumber(iossNumber)) {
            errors.push('Invalid IOSS number format. Must start with "IM" followed by 12 digits (e.g., IM1234567890123)');
        }

        // Validate reporting period
        if (!this.isValidReportingPeriod(period)) {
            errors.push('Invalid reporting period. Must be in YYYY-MM format and not in the future');
        }

        // Validate currency
        if (!this.isValidCurrency(currency)) {
            errors.push('Invalid currency. Must be EUR, USD, or GBP');
        }

        return errors;
    }

    // IOSS Number Validation
    isValidIOSSNumber(iossNumber) {
        if (!iossNumber) return false;

        // IOSS number format: IM + 12 digits
        const iossPattern = /^IM\d{12}$/;
        return iossPattern.test(iossNumber.toUpperCase());
    }

    // Reporting Period Validation
    isValidReportingPeriod(period) {
        if (!period) return false;

        // Check format YYYY-MM
        const periodPattern = /^\d{4}-\d{2}$/;
        if (!periodPattern.test(period)) return false;

        // Parse and validate date
        const [year, month] = period.split('-').map(Number);

        // Validate year (reasonable range)
        if (year < 2021 || year > new Date().getFullYear() + 1) return false;

        // Validate month
        if (month < 1 || month > 12) return false;

        // Check if period is not in the future (allow current month)
        const periodDate = new Date(year, month - 1, 1);
        const currentDate = new Date();
        const currentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);

        return periodDate <= currentMonth;
    }

    // Currency Validation
    isValidCurrency(currency) {
        const validCurrencies = ['EUR', 'USD', 'GBP'];
        return validCurrencies.includes(currency);
    }

    // Enhanced threshold validation
    validateIOSSThreshold(amount, currency) {
        // Convert to EUR for threshold check
        const amountEUR = currency === 'EUR' ? amount : this.convertToEUR(amount, currency);

        if (amountEUR > this.iossEligibilityThreshold) {
            return {
                isValid: false,
                reason: `Amount €${amountEUR.toFixed(2)} exceeds IOSS threshold of €${this.iossEligibilityThreshold}`
            };
        }

        return { isValid: true };
    }

    // Core processing methods
    async processOrderData() {
        this.issues = [];
        this.processedData = {
            orders: [],
            summary: {
                totalOrders: 0,
                iossEligibleOrders: 0,
                totalVatCalculated: 0,
                totalVatShopify: 0,
                countryCount: 0,
                totalValueEUR: 0,
                excludedOrders: 0,
                excludedValue: 0
            },
            breakdown: {}
        };

        this.showProcessingStatus('Processing orders...');

        try {
            // Process each order
            let processedCount = 0;
            for (const order of this.csvData) {
                const processedOrder = await this.processOrder(order);
                if (processedOrder) {
                    this.processedData.orders.push(processedOrder);
                    processedCount++;
                }
                
                // Update progress for large datasets
                if (processedCount % 100 === 0) {
                    this.showProcessingStatus(`Processing orders... (${processedCount}/${this.csvData.length})`);
                    await this.sleep(1); // Allow UI to update
                }
            }

            // Calculate summary and breakdown
            this.calculateSummary();
            this.displayResults();

            console.log(`✅ Successfully processed ${processedCount} orders`);

        } catch (error) {
            this.addIssue('error', `Processing failed: ${error.message}`);
            this.showProcessingStatus('Processing failed');
            console.error('❌ Order processing failed:', error);
        }
    }

    async processOrder(order) {
        try {
            // Extract basic order info
            const orderId = order.Name || order['Order ID'] || order['Order Number'] || 'Unknown';
            
            // Extract financial data with multiple possible column names
            const subtotal = this.parseAmount(order.Subtotal || order['Line Item Subtotal'] || 0);
            const taxes = this.parseAmount(order.Taxes || order['Tax Amount'] || order['Total Tax'] || 0);
            const shipping = this.parseAmount(order.Shipping || order['Shipping Amount'] || 0);
            const total = this.parseAmount(order.Total || order['Total Amount'] || 0);

            // Get currency (priority: Currency column > company default > EUR)
            let currency = order.Currency || order['Currency Code'] || this.companyInfo.currency || 'EUR';
            currency = currency.toUpperCase();
            this.detectedCurrencies.add(currency);

            // Convert to EUR if needed
            const subtotalEUR = await this.convertToEUR(subtotal, currency);
            const taxesEUR = await this.convertToEUR(taxes, currency);
            const shippingEUR = await this.convertToEUR(shipping, currency);
            const totalEUR = await this.convertToEUR(total, currency);

            // Extract location data
            const country = this.extractCountry(order);
            const vatRate = this.extractVATRate(order);
            
            // CRITICAL FIX: Enhanced IOSS eligibility check
            const isEUDestination = this.isEUDestination(country);
            const isUnderThreshold = totalEUR <= this.iossEligibilityThreshold;
            const isB2CTransaction = this.isB2CTransaction(order);
            const isGoodsSupply = this.isGoodsSupply(order);
            const hasValidVATRate = vatRate > 0;

            // All conditions must be met for IOSS eligibility
            const isIOSSEligible = isEUDestination && isUnderThreshold && isB2CTransaction && isGoodsSupply && hasValidVATRate;

            // Determine exclusion reason if not eligible
            let exclusionReason = '';
            if (!isEUDestination) exclusionReason = 'Non-EU destination';
            else if (!isUnderThreshold) exclusionReason = `Over €150 threshold (€${totalEUR.toFixed(2)})`;
            else if (!isB2CTransaction) exclusionReason = 'B2B transaction (customer has VAT number)';
            else if (!isGoodsSupply) exclusionReason = 'Service/Digital product';
            else if (!hasValidVATRate) exclusionReason = 'Invalid or zero VAT rate';

            // Extract order date
            const orderDate = order['Created at'] || order['Order Date'] || order['Date'] || new Date().toISOString();

            const processedOrder = {
                orderId,
                originalCurrency: currency,
                subtotal: subtotalEUR,
                taxes: taxesEUR,
                shipping: shippingEUR,
                total: totalEUR,
                country,
                vatRate,
                isIOSSEligible,
                isEUDestination,
                isUnderThreshold,
                isB2CTransaction,
                isGoodsSupply,
                exclusionReason,
                orderDate,
                customerEmail: order['Email'] || order['Customer Email'] || '',
                customerVATNumber: order['Customer VAT Number'] || order['VAT Number'] || order['Tax Number'] || '',
                requiresShipping: order['Lineitem requires shipping'] || 'true',
                productType: order['Lineitem name'] || '',
                shippingMethod: order['Shipping Method'] || order['Shipping'] || '',
                fulfillmentStatus: order['Fulfillment Status'] || '',
                financialStatus: order['Financial Status'] || order['Payment Status'] || ''
            };

            return processedOrder;

        } catch (error) {
            this.addIssue('warning', `Error processing order ${order.Name || 'Unknown'}: ${error.message}`);
            return null;
        }
    }

    parseAmount(value) {
        if (typeof value === 'number') return value;
        if (typeof value === 'string') {
            // Remove currency symbols and parse
            const cleaned = value.replace(/[^\d.-]/g, '');
            return parseFloat(cleaned) || 0;
        }
        return 0;
    }

    async convertToEUR(amount, fromCurrency) {
        if (fromCurrency === 'EUR') return amount;

        // ENHANCED: Try ECB rates first, then fallback
        let rate = await this.getECBExchangeRate(fromCurrency);

        if (!rate) {
            // Fallback to stored rates
            rate = this.exchangeRates[fromCurrency] || this.fallbackRates[fromCurrency] || 1;
            console.warn(`⚠️ Using fallback exchange rate for ${fromCurrency}: ${rate}`);
        }

        return amount * rate;
    }

    // ENHANCED: ECB Exchange Rate Integration
    async getECBExchangeRate(currency) {
        try {
            // Check if we have a recent cached rate (within 24 hours)
            const cacheKey = `ecb_rate_${currency}`;
            const cachedData = localStorage.getItem(cacheKey);

            if (cachedData) {
                const { rate, timestamp } = JSON.parse(cachedData);
                const age = Date.now() - timestamp;
                const maxAge = 24 * 60 * 60 * 1000; // 24 hours

                if (age < maxAge) {
                    console.log(`📊 Using cached ECB rate for ${currency}: ${rate}`);
                    return rate;
                }
            }

            // Fetch fresh rate from ECB
            const rate = await this.fetchECBRate(currency);

            if (rate) {
                // Cache the rate
                localStorage.setItem(cacheKey, JSON.stringify({
                    rate: rate,
                    timestamp: Date.now()
                }));

                console.log(`📊 Fetched fresh ECB rate for ${currency}: ${rate}`);
                return rate;
            }

        } catch (error) {
            console.warn(`⚠️ Failed to get ECB rate for ${currency}:`, error.message);
        }

        return null;
    }

    // Fetch rate from ECB API
    async fetchECBRate(currency) {
        try {
            // ECB provides daily rates - use the latest available
            const response = await fetch(`https://api.exchangerate-api.com/v4/latest/EUR`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();

            if (data.rates && data.rates[currency]) {
                // ECB gives EUR to other currency, we need other currency to EUR
                return 1 / data.rates[currency];
            }

            throw new Error(`Rate not found for ${currency}`);

        } catch (error) {
            console.warn(`⚠️ ECB API fetch failed for ${currency}:`, error.message);

            // Try alternative ECB endpoint
            try {
                const altResponse = await fetch(`https://api.fixer.io/latest?base=EUR&symbols=${currency}`);
                if (altResponse.ok) {
                    const altData = await altResponse.json();
                    if (altData.rates && altData.rates[currency]) {
                        return 1 / altData.rates[currency];
                    }
                }
            } catch (altError) {
                console.warn(`⚠️ Alternative ECB API also failed:`, altError.message);
            }

            return null;
        }
    }

    // Validate exchange rate date for IOSS compliance
    validateExchangeRateDate(rateDate, transactionDate) {
        // For IOSS, exchange rates should be from the same month as the transaction
        const ratePeriod = rateDate.substring(0, 7); // YYYY-MM
        const transactionPeriod = transactionDate.substring(0, 7); // YYYY-MM

        return ratePeriod === transactionPeriod;
    }

    // Get monthly average rate for IOSS compliance
    async getMonthlyAverageRate(currency, year, month) {
        try {
            const cacheKey = `ecb_monthly_${currency}_${year}_${month}`;
            const cachedRate = localStorage.getItem(cacheKey);

            if (cachedRate) {
                return parseFloat(cachedRate);
            }

            // In a real implementation, you would fetch historical daily rates
            // and calculate the monthly average. For now, use current rate.
            const currentRate = await this.getECBExchangeRate(currency);

            if (currentRate) {
                localStorage.setItem(cacheKey, currentRate.toString());
            }

            return currentRate;

        } catch (error) {
            console.warn(`⚠️ Failed to get monthly average rate:`, error.message);
            return null;
        }
    }

    isEUDestination(countryCode) {
        return this.euCountries.includes(countryCode);
    }

    extractCountry(order) {
        // Try multiple possible column names for country
        const country = order['Shipping Country Code'] || 
                       order['Billing Country Code'] || 
                       order['Country Code'] ||
                       order['Country'] ||
                       order['Shipping Country'] ||
                       order['Billing Country'] ||
                       'XX';
        
        return country.toUpperCase().substring(0, 2);
    }

    extractVATRate(order) {
        // Try to extract VAT rate from tax name or rate columns
        const taxName = order['Tax name'] || order['Tax Name'] || order['Tax 1 Name'] || '';
        const taxRate = order['Tax Rate'] || order['Tax 1 Rate'] || '';
        
        // Look for percentage in tax name
        let match = taxName.match(/(\d+(?:\.\d+)?)%/);
        if (match) return parseFloat(match[1]);
        
        // Look for rate in tax rate column
        if (taxRate) {
            const cleaned = taxRate.toString().replace(/[^\d.]/g, '');
            const rate = parseFloat(cleaned);
            if (rate > 0) return rate;
        }
        
        // Default based on country
        return this.getDefaultVATRate(this.extractCountry(order));
    }

    getDefaultVATRate(countryCode) {
        const defaultRates = {
            'AT': 20, 'BE': 21, 'BG': 20, 'HR': 25, 'CY': 19, 'CZ': 21,
            'DK': 25, 'EE': 22, 'FI': 25.5, 'FR': 20, 'DE': 19, 'GR': 24,
            'EL': 24, 'HU': 27, 'IE': 23, 'IT': 22, 'LV': 21, 'LT': 21,
            'LU': 17, 'MT': 18, 'NL': 21, 'PL': 23, 'PT': 23, 'RO': 19,
            'SK': 23, 'SI': 22, 'ES': 21, 'SE': 25, 'XI': 20  // Added XI for Northern Ireland
        };
        return defaultRates[countryCode] || 0;
    }

    // CRITICAL FIX: B2C Transaction Verification
    isB2CTransaction(order) {
        // IOSS only applies to B2C transactions (no customer VAT number)
        const vatNumber = (order['Customer VAT Number'] || order['VAT Number'] || order['Tax Number'] || '').trim();

        if (vatNumber && vatNumber.length > 0) {
            // Check if it's a valid VAT number format
            if (this.isValidVATNumberFormat(vatNumber)) {
                return false; // B2B transaction
            }
        }

        return true; // No VAT number = B2C transaction
    }

    // VAT Number Format Validation
    isValidVATNumberFormat(vatNumber) {
        // Basic VAT number format validation for EU countries
        const vatPatterns = {
            'AT': /^ATU\d{8}$/,
            'BE': /^BE[01]\d{9}$/,
            'BG': /^BG\d{9,10}$/,
            'HR': /^HR\d{11}$/,
            'CY': /^CY\d{8}[A-Z]$/,
            'CZ': /^CZ\d{8,10}$/,
            'DK': /^DK\d{8}$/,
            'EE': /^EE\d{9}$/,
            'FI': /^FI\d{8}$/,
            'FR': /^FR[A-Z0-9]{2}\d{9}$/,
            'DE': /^DE\d{9}$/,
            'GR': /^(EL|GR)\d{9}$/,
            'HU': /^HU\d{8}$/,
            'IE': /^IE\d[A-Z0-9]\d{5}[A-Z]$/,
            'IT': /^IT\d{11}$/,
            'LV': /^LV\d{11}$/,
            'LT': /^LT(\d{9}|\d{12})$/,
            'LU': /^LU\d{8}$/,
            'MT': /^MT\d{8}$/,
            'NL': /^NL\d{9}B\d{2}$/,
            'PL': /^PL\d{10}$/,
            'PT': /^PT\d{9}$/,
            'RO': /^RO\d{2,10}$/,
            'SK': /^SK\d{10}$/,
            'SI': /^SI\d{8}$/,
            'ES': /^ES[A-Z0-9]\d{7}[A-Z0-9]$/,
            'SE': /^SE\d{12}$/
        };

        // Extract country code from VAT number
        const countryCode = vatNumber.substring(0, 2).toUpperCase();
        const pattern = vatPatterns[countryCode];

        return pattern ? pattern.test(vatNumber.toUpperCase()) : false;
    }

    // CRITICAL FIX: Supply Type Classification
    isGoodsSupply(order) {
        // Check if this is a goods supply (physical products requiring shipping)
        const requiresShipping = (order['Lineitem requires shipping'] || 'true').toLowerCase();

        if (requiresShipping === 'false' || requiresShipping === 'no') {
            // Likely a service or digital product
            return false;
        }

        // Check product type for digital services
        const productName = (order['Lineitem name'] || '').toLowerCase();
        const digitalKeywords = ['download', 'digital', 'ebook', 'software', 'license', 'subscription', 'service', 'consultation', 'course', 'training'];

        if (digitalKeywords.some(keyword => productName.includes(keyword))) {
            return false; // Digital service/product
        }

        return true; // Default to goods if shipping is required
    }

    calculateSummary() {
        const allOrders = this.processedData.orders;
        const eligibleOrders = allOrders.filter(o => o.isIOSSEligible);

        this.processedData.summary = {
            totalOrders: allOrders.length,
            iossEligibleOrders: eligibleOrders.length,
            excludedOrders: allOrders.length - eligibleOrders.length,
            totalVatCalculated: eligibleOrders.reduce((sum, o) => sum + o.taxes, 0),
            totalVatShopify: eligibleOrders.reduce((sum, o) => sum + o.taxes, 0), // Same for now
            totalValueEUR: eligibleOrders.reduce((sum, o) => sum + o.total, 0),
            excludedValue: allOrders.filter(o => !o.isIOSSEligible).reduce((sum, o) => sum + o.total, 0),
            countryCount: new Set(eligibleOrders.map(o => o.country)).size
        };

        // Create breakdown by country and VAT rate
        this.processedData.breakdown = {};
        eligibleOrders.forEach(order => {
            const key = `${order.country}-${order.vatRate}`;
            if (!this.processedData.breakdown[key]) {
                this.processedData.breakdown[key] = {
                    country: order.country,
                    vatRate: order.vatRate,
                    orders: 0,
                    netValue: 0,
                    vatAmount: 0,
                    shopifyNetValue: 0,
                    shopifyVatAmount: 0
                };
            }

            const breakdown = this.processedData.breakdown[key];
            breakdown.orders++;

            // FIXED: Calculate net value correctly for IOSS reporting
            // For IOSS, we need the taxable amount (net value excluding VAT)
            // If we have the total including VAT, we need to calculate backwards
            const totalIncludingVAT = order.total;
            const vatRateDecimal = order.vatRate / 100;
            const calculatedNetValue = totalIncludingVAT / (1 + vatRateDecimal);
            const calculatedVATAmount = totalIncludingVAT - calculatedNetValue;

            breakdown.netValue += calculatedNetValue;
            breakdown.vatAmount += calculatedVATAmount;

            // Keep Shopify values as they were reported
            breakdown.shopifyNetValue += order.subtotal + order.shipping;
            breakdown.shopifyVatAmount += order.taxes;
        });

        console.log('📊 Summary calculated:', this.processedData.summary);
    }

    displayResults() {
        const currencySymbol = this.getCurrencySymbol(this.companyInfo.currency);
        const summary = this.processedData.summary;

        // Update summary statistics
        this.updateElement('total-orders', summary.totalOrders);
        this.updateElement('ioss-orders', summary.iossEligibleOrders);
        this.updateElement('total-vat-calculated', `${currencySymbol}${summary.totalVatCalculated.toFixed(2)}`);
        this.updateElement('total-vat-shopify', `${currencySymbol}${summary.totalVatShopify.toFixed(2)}`);
        this.updateElement('countries-count', summary.countryCount);

        // Update breakdown table
        const tableBody = document.getElementById('country-breakdown');
        if (tableBody) {
            tableBody.innerHTML = '';

            Object.values(this.processedData.breakdown).forEach(data => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${data.country}</td>
                    <td>${data.vatRate.toFixed(1)}%</td>
                    <td>${data.orders}</td>
                    <td>${currencySymbol}${data.netValue.toFixed(2)}</td>
                    <td>${currencySymbol}${data.vatAmount.toFixed(2)}</td>
                    <td>${currencySymbol}${data.shopifyNetValue.toFixed(2)}</td>
                    <td>${currencySymbol}${data.shopifyVatAmount.toFixed(2)}</td>
                `;
                tableBody.appendChild(row);
            });
        }

        // Show issues
        this.displayIssues();

        // Show results section
        this.showElement('results-section');
        this.showElement('export-buttons');
        this.hideProcessingStatus();
        
        console.log('✅ Results displayed');
    }

    processRefunds() {
        this.showRefundsProcessingStatus('Processing refunds...');

        try {
            // ENHANCED: Comprehensive refunds processing
            const refunds = this.identifyRefunds(this.refundsData);
            const processedRefunds = this.processRefundsForIOSS(refunds);
            const refundsByPeriod = this.groupRefundsByOriginalPeriod(processedRefunds);
            const adjustmentCalculations = this.calculateRefundAdjustments(refundsByPeriod);

            // Update refunds results with enhanced data
            this.updateElement('refunds-total', refunds.length);
            this.updateElement('refunds-eligible', processedRefunds.eligible.length);
            this.updateElement('refunds-excluded', processedRefunds.excluded.length);
            this.updateElement('refunds-periods', Object.keys(refundsByPeriod).length);

            // Store comprehensive processed refunds data
            this.processedRefunds = {
                allRefunds: refunds,
                eligibleRefunds: processedRefunds.eligible,
                excludedRefunds: processedRefunds.excluded,
                refundsByPeriod: refundsByPeriod,
                adjustmentCalculations: adjustmentCalculations,
                periods: Object.keys(refundsByPeriod)
            };

            // Display detailed refunds breakdown
            this.displayRefundsBreakdown();

            this.showElement('refunds-results-section');
            this.showElement('refunds-results-content');
            this.showElement('refunds-export-buttons');
            this.hideRefundsProcessingStatus();

            console.log(`✅ Enhanced refunds processing completed: ${refunds.length} total, ${processedRefunds.eligible.length} eligible, ${Object.keys(refundsByPeriod).length} periods`);

        } catch (error) {
            this.addIssue('error', `Refunds processing failed: ${error.message}`);
            this.hideRefundsProcessingStatus();
            console.error('❌ Refunds processing failed:', error);
        }
    }

    // ENHANCED: Identify refunds with better logic
    identifyRefunds(data) {
        return data.filter(row => {
            const taxAmount = parseFloat(row['Tax amount'] || row['Tax Amount'] || 0);
            const totalAmount = parseFloat(row['Total'] || row['Total Amount'] || 0);

            // Check for negative tax amounts or negative totals
            return taxAmount < 0 || totalAmount < 0;
        });
    }

    // ENHANCED: Process refunds for IOSS eligibility with detailed checks
    processRefundsForIOSS(refunds) {
        const eligible = [];
        const excluded = [];

        refunds.forEach(refund => {
            const country = this.extractCountry(refund);
            const taxAmount = Math.abs(parseFloat(refund['Tax amount'] || refund['Tax Amount'] || 0));
            const totalAmount = Math.abs(parseFloat(refund['Total'] || refund['Total Amount'] || 0));

            // Enhanced eligibility check
            const isEUDestination = this.isEUDestination(country);
            const isSignificantAmount = taxAmount > 0.01 || totalAmount > 0.01;
            const hasValidDate = this.extractRefundDate(refund) !== null;

            const processedRefund = {
                ...refund,
                country: country,
                taxAmount: taxAmount,
                totalAmount: totalAmount,
                refundDate: this.extractRefundDate(refund),
                originalPeriod: this.estimateOriginalPeriod(refund),
                vatRate: this.extractVATRate(refund),
                isEUDestination: isEUDestination,
                isSignificantAmount: isSignificantAmount,
                hasValidDate: hasValidDate
            };

            if (isEUDestination && isSignificantAmount && hasValidDate) {
                eligible.push(processedRefund);
            } else {
                processedRefund.exclusionReason = this.getRefundExclusionReason(processedRefund);
                excluded.push(processedRefund);
            }
        });

        return { eligible, excluded };
    }

    // Extract refund date with multiple fallbacks
    extractRefundDate(refund) {
        const dateFields = ['Day', 'Date', 'Created at', 'Refund Date', 'Transaction Date'];

        for (const field of dateFields) {
            const dateValue = refund[field];
            if (dateValue) {
                const parsedDate = new Date(dateValue);
                if (!isNaN(parsedDate.getTime())) {
                    return parsedDate;
                }
            }
        }

        return null;
    }

    // Estimate original transaction period from refund
    estimateOriginalPeriod(refund) {
        const refundDate = this.extractRefundDate(refund);
        if (!refundDate) return 'Unknown';

        // For IOSS, assume original transaction was in previous months
        // This is a simplified estimation - in practice, you'd match with original orders
        const estimatedOriginal = new Date(refundDate);
        estimatedOriginal.setMonth(estimatedOriginal.getMonth() - 1);

        return `${estimatedOriginal.getFullYear()}-${String(estimatedOriginal.getMonth() + 1).padStart(2, '0')}`;
    }

    // Get exclusion reason for refunds
    getRefundExclusionReason(refund) {
        if (!refund.isEUDestination) return 'Non-EU destination';
        if (!refund.isSignificantAmount) return 'Insignificant amount';
        if (!refund.hasValidDate) return 'Invalid or missing date';
        return 'Unknown exclusion reason';
    }

    // Group refunds by original reporting period
    groupRefundsByOriginalPeriod(processedRefunds) {
        const refundsByPeriod = {};

        processedRefunds.eligible.forEach(refund => {
            const period = refund.originalPeriod;
            if (!refundsByPeriod[period]) {
                refundsByPeriod[period] = [];
            }
            refundsByPeriod[period].push(refund);
        });

        return refundsByPeriod;
    }

    // Calculate adjustment amounts for each period
    calculateRefundAdjustments(refundsByPeriod) {
        const adjustments = {};

        Object.entries(refundsByPeriod).forEach(([period, refunds]) => {
            const totalTaxAmount = refunds.reduce((sum, refund) => sum + refund.taxAmount, 0);
            const totalNetAmount = refunds.reduce((sum, refund) => {
                const vatRate = refund.vatRate || 0;
                const netAmount = refund.totalAmount / (1 + vatRate / 100);
                return sum + netAmount;
            }, 0);

            adjustments[period] = {
                refundCount: refunds.length,
                totalTaxAmount: totalTaxAmount,
                totalNetAmount: totalNetAmount,
                countries: [...new Set(refunds.map(r => r.country))],
                requiresAdjustment: totalTaxAmount > 0.01
            };
        });

        return adjustments;
    }

    // Display detailed refunds breakdown
    displayRefundsBreakdown() {
        if (!this.processedRefunds) return;

        // Update period breakdown table
        const periodTableBody = document.getElementById('refunds-period-breakdown');
        if (periodTableBody) {
            periodTableBody.innerHTML = '';

            Object.entries(this.processedRefunds.adjustmentCalculations).forEach(([period, adjustment]) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${period}</td>
                    <td>${adjustment.refundCount}</td>
                    <td>${adjustment.countries.length} (${adjustment.countries.join(', ')})</td>
                    <td>€${adjustment.totalTaxAmount.toFixed(2)}</td>
                `;
                periodTableBody.appendChild(row);
            });
        }

        // Update country breakdown table
        const countryTableBody = document.getElementById('refunds-country-breakdown');
        if (countryTableBody) {
            countryTableBody.innerHTML = '';

            // Group refunds by country and VAT rate
            const countryBreakdown = {};
            this.processedRefunds.eligibleRefunds.forEach(refund => {
                const key = `${refund.country}-${refund.vatRate}`;
                if (!countryBreakdown[key]) {
                    countryBreakdown[key] = {
                        country: refund.country,
                        vatRate: refund.vatRate,
                        count: 0,
                        totalAmount: 0
                    };
                }
                countryBreakdown[key].count++;
                countryBreakdown[key].totalAmount += refund.taxAmount;
            });

            Object.values(countryBreakdown).forEach(data => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${data.country}</td>
                    <td>${data.vatRate.toFixed(1)}%</td>
                    <td>${data.count}</td>
                    <td>€${data.totalAmount.toFixed(2)}</td>
                `;
                countryTableBody.appendChild(row);
            });
        }
    }

    // Export functions - FIXED: All export functions now properly implemented

    exportCSV() {
        try {
            const csvContent = this.generateCSVContent();
            this.downloadFile(csvContent, 'ioss-orders-report.csv', 'text/csv');
            console.log('✅ CSV export completed');
        } catch (error) {
            this.addIssue('error', `CSV export failed: ${error.message}`);
        }
    }

    exportIOSSCompliant() {
        try {
            const csvContent = this.generateIOSSCompliantCSV();
            this.downloadFile(csvContent, 'ioss-compliant-report.csv', 'text/csv');
            console.log('✅ IOSS compliant export completed');
        } catch (error) {
            this.addIssue('error', `IOSS compliant export failed: ${error.message}`);
        }
    }

    exportDetailed() {
        try {
            const content = this.generateDetailedReport();
            this.downloadFile(content, 'detailed-report.txt', 'text/plain');
            console.log('✅ Detailed export completed');
        } catch (error) {
            this.addIssue('error', `Detailed export failed: ${error.message}`);
        }
    }

    exportXML() {
        try {
            const xmlContent = this.generateXMLReport();
            this.downloadFile(xmlContent, 'ioss-report.xml', 'application/xml');
            console.log('✅ XML export completed');
        } catch (error) {
            this.addIssue('error', `XML export failed: ${error.message}`);
        }
    }

    exportEstonianVATXML() {
        try {
            const xmlContent = this.generateEstonianVATXML();
            this.downloadFile(xmlContent, 'estonian-vat-report.xml', 'application/xml');
            console.log('✅ Estonian VAT XML export completed');
        } catch (error) {
            this.addIssue('error', `Estonian VAT XML export failed: ${error.message}`);
        }
    }

    // FIXED: Refunds export functions - These were completely missing!
    exportRefundsCSV() {
        try {
            if (!this.processedRefunds) {
                throw new Error('No refunds data available. Please process refunds first.');
            }
            const csvContent = this.generateRefundsCSV();
            this.downloadFile(csvContent, 'refunds-report.csv', 'text/csv');
            console.log('✅ Refunds CSV export completed');
        } catch (error) {
            this.addIssue('error', `Refunds CSV export failed: ${error.message}`);
        }
    }

    exportRefundsXML() {
        try {
            if (!this.processedRefunds) {
                throw new Error('No refunds data available. Please process refunds first.');
            }
            const xmlContent = this.generateRefundsXML();
            this.downloadFile(xmlContent, 'refunds-report.xml', 'application/xml');
            console.log('✅ Refunds XML export completed');
        } catch (error) {
            this.addIssue('error', `Refunds XML export failed: ${error.message}`);
        }
    }

    exportRefundsDetailed() {
        try {
            if (!this.processedRefunds) {
                throw new Error('No refunds data available. Please process refunds first.');
            }
            const content = this.generateRefundsDetailedReport();
            this.downloadFile(content, 'refunds-detailed-report.txt', 'text/plain');
            console.log('✅ Refunds detailed export completed');
        } catch (error) {
            this.addIssue('error', `Refunds detailed export failed: ${error.message}`);
        }
    }

    // Content generation functions
    generateCSVContent() {
        let csv = 'Country,VATRate,Orders,NetValue,VATAmount,Currency,Period\n';
        
        Object.values(this.processedData.breakdown).forEach(data => {
            csv += `${data.country},${data.vatRate},${data.orders},${data.netValue.toFixed(2)},${data.vatAmount.toFixed(2)},${this.companyInfo.currency},${this.companyInfo.period}\n`;
        });
        
        return csv;
    }

    generateIOSSCompliantCSV() {
        let csv = 'CountryCode,SupplyType,VATRateType,VATRate,TaxableAmount,VATAmount,Currency,ReportingPeriod\n';
        
        Object.values(this.processedData.breakdown).forEach(data => {
            const rateType = this.determineVATRateType(data.vatRate, data.country);
            csv += `${data.country},GOODS,${rateType},${data.vatRate},${data.netValue.toFixed(2)},${data.vatAmount.toFixed(2)},${this.companyInfo.currency},${this.companyInfo.period}\n`;
        });
        
        return csv;
    }

    determineVATRateType(rate, country) {
        // Enhanced VAT rate categorization with country-specific rules
        const countryRates = this.getCountryVATRates(country);

        if (!countryRates) {
            // Fallback to basic categorization
            if (rate >= 20) return 'STANDARD';
            if (rate >= 10) return 'REDUCED';
            if (rate >= 5) return 'SUPER_REDUCED';
            return 'PARKING';
        }

        // Check against country-specific rates
        if (Math.abs(rate - countryRates.standard) < 0.1) return 'STANDARD';

        // Check reduced rates
        if (countryRates.reduced) {
            for (const reducedRate of countryRates.reduced) {
                if (Math.abs(rate - reducedRate) < 0.1) return 'REDUCED';
            }
        }

        // Check super reduced rates
        if (countryRates.superReduced) {
            for (const superReducedRate of countryRates.superReduced) {
                if (Math.abs(rate - superReducedRate) < 0.1) return 'SUPER_REDUCED';
            }
        }

        // Check parking rate
        if (countryRates.parking && Math.abs(rate - countryRates.parking) < 0.1) {
            return 'PARKING';
        }

        // Check zero rate
        if (rate === 0) return 'ZERO';

        // Default fallback
        if (rate >= 20) return 'STANDARD';
        if (rate >= 10) return 'REDUCED';
        if (rate >= 5) return 'SUPER_REDUCED';
        return 'PARKING';
    }

    // Enhanced country-specific VAT rates
    getCountryVATRates(countryCode) {
        const countryVATRates = {
            'AT': { standard: 20, reduced: [10, 13], superReduced: [], parking: 0 },
            'BE': { standard: 21, reduced: [6, 12], superReduced: [], parking: 0 },
            'BG': { standard: 20, reduced: [9], superReduced: [], parking: 0 },
            'HR': { standard: 25, reduced: [5, 13], superReduced: [], parking: 0 },
            'CY': { standard: 19, reduced: [5, 9], superReduced: [], parking: 0 },
            'CZ': { standard: 21, reduced: [10, 15], superReduced: [], parking: 0 },
            'DK': { standard: 25, reduced: [], superReduced: [], parking: 0 },
            'EE': { standard: 22, reduced: [9], superReduced: [], parking: 0 },
            'FI': { standard: 25.5, reduced: [10, 14], superReduced: [], parking: 0 },
            'FR': { standard: 20, reduced: [5.5, 10], superReduced: [2.1], parking: 0 },
            'DE': { standard: 19, reduced: [7], superReduced: [], parking: 0 },
            'GR': { standard: 24, reduced: [6, 13], superReduced: [], parking: 0 },
            'EL': { standard: 24, reduced: [6, 13], superReduced: [], parking: 0 },
            'HU': { standard: 27, reduced: [5, 18], superReduced: [], parking: 0 },
            'IE': { standard: 23, reduced: [9, 13.5], superReduced: [4.8], parking: 0 },
            'IT': { standard: 22, reduced: [5, 10], superReduced: [4], parking: 0 },
            'LV': { standard: 21, reduced: [5, 12], superReduced: [], parking: 0 },
            'LT': { standard: 21, reduced: [5, 9], superReduced: [], parking: 0 },
            'LU': { standard: 17, reduced: [8], superReduced: [3], parking: 14 },
            'MT': { standard: 18, reduced: [5, 7], superReduced: [], parking: 0 },
            'NL': { standard: 21, reduced: [9], superReduced: [], parking: 0 },
            'PL': { standard: 23, reduced: [5, 8], superReduced: [], parking: 0 },
            'PT': { standard: 23, reduced: [6, 13], superReduced: [], parking: 0 },
            'RO': { standard: 19, reduced: [5, 9], superReduced: [], parking: 0 },
            'SK': { standard: 23, reduced: [10], superReduced: [], parking: 0 },
            'SI': { standard: 22, reduced: [5, 9.5], superReduced: [], parking: 0 },
            'ES': { standard: 21, reduced: [10], superReduced: [4], parking: 0 },
            'SE': { standard: 25, reduced: [6, 12], superReduced: [], parking: 0 },
            'XI': { standard: 20, reduced: [5], superReduced: [], parking: 0 }
        };

        return countryVATRates[countryCode];
    }

    // Enhanced VAT rate extraction with product-specific logic
    getApplicableVATRate(country, productType, extractedRate) {
        const countryRates = this.getCountryVATRates(country);
        if (!countryRates) return extractedRate || this.getDefaultVATRate(country);

        // If we have a valid extracted rate, use it
        if (extractedRate && extractedRate > 0) return extractedRate;

        // Apply product-specific rates
        const productName = productType?.toLowerCase() || '';

        // Books and publications (often reduced rate)
        if (productName.includes('book') || productName.includes('magazine') || productName.includes('newspaper')) {
            return countryRates.reduced?.[0] || countryRates.standard;
        }

        // Food and beverages (often reduced rate)
        if (productName.includes('food') || productName.includes('drink') || productName.includes('beverage')) {
            return countryRates.reduced?.[0] || countryRates.standard;
        }

        // Children's items (often reduced rate)
        if (productName.includes('child') || productName.includes('baby') || productName.includes('kid')) {
            return countryRates.reduced?.[0] || countryRates.standard;
        }

        // Medical items (often reduced or super reduced)
        if (productName.includes('medical') || productName.includes('health') || productName.includes('medicine')) {
            return countryRates.superReduced?.[0] || countryRates.reduced?.[0] || countryRates.standard;
        }

        // Default to standard rate
        return countryRates.standard;
    }

    generateDetailedReport() {
        let report = `IOSS Detailed Report\n`;
        report += `${'='.repeat(50)}\n`;
        report += `Generated: ${new Date().toISOString()}\n`;
        report += `Company: ${this.companyInfo.name}\n`;
        report += `IOSS Number: ${this.companyInfo.iossNumber}\n`;
        report += `Reporting Period: ${this.companyInfo.period}\n`;
        report += `Base Currency: ${this.companyInfo.currency}\n\n`;
        
        const summary = this.processedData.summary;
        report += `SUMMARY\n`;
        report += `${'='.repeat(30)}\n`;
        report += `Total Orders Processed: ${summary.totalOrders}\n`;
        report += `IOSS Eligible Orders: ${summary.iossEligibleOrders}\n`;
        report += `Excluded Orders: ${summary.excludedOrders}\n`;
        report += `Total VAT Collected: ${this.getCurrencySymbol(this.companyInfo.currency)}${summary.totalVatCalculated.toFixed(2)}\n`;
        report += `Total Value (EUR): €${summary.totalValueEUR.toFixed(2)}\n`;
        report += `Countries Served: ${summary.countryCount}\n\n`;
        
        report += `BREAKDOWN BY COUNTRY AND VAT RATE\n`;
        report += `${'='.repeat(40)}\n`;
        Object.values(this.processedData.breakdown).forEach(data => {
            report += `${data.country} (${data.vatRate.toFixed(1)}%): ${data.orders} orders, `;
            report += `€${data.netValue.toFixed(2)} net, €${data.vatAmount.toFixed(2)} VAT\n`;
        });
        
        if (this.issues.length > 0) {
            report += `\nISSUES AND WARNINGS\n`;
            report += `${'='.repeat(30)}\n`;
            this.issues.forEach(issue => {
                report += `[${issue.type.toUpperCase()}] ${issue.message}\n`;
            });
        }
        
        return report;
    }

    generateXMLReport() {
        let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
        xml += '<IOSSReport>\n';
        xml += `  <CompanyInfo>\n`;
        xml += `    <Name>${this.escapeXML(this.companyInfo.name)}</Name>\n`;
        xml += `    <IOSSNumber>${this.companyInfo.iossNumber}</IOSSNumber>\n`;
        xml += `    <ReportingPeriod>${this.companyInfo.period}</ReportingPeriod>\n`;
        xml += `    <Currency>${this.companyInfo.currency}</Currency>\n`;
        xml += `  </CompanyInfo>\n`;
        xml += `  <Summary>\n`;
        xml += `    <TotalOrders>${this.processedData.summary.totalOrders}</TotalOrders>\n`;
        xml += `    <EligibleOrders>${this.processedData.summary.iossEligibleOrders}</EligibleOrders>\n`;
        xml += `    <TotalVAT>${this.processedData.summary.totalVatCalculated.toFixed(2)}</TotalVAT>\n`;
        xml += `    <CountriesCount>${this.processedData.summary.countryCount}</CountriesCount>\n`;
        xml += `  </Summary>\n`;
        xml += '  <VATReturns>\n';
        
        Object.values(this.processedData.breakdown).forEach(data => {
            xml += '    <VATReturn>\n';
            xml += `      <Country>${data.country}</Country>\n`;
            xml += `      <VATRate>${data.vatRate.toFixed(2)}</VATRate>\n`;
            xml += `      <Orders>${data.orders}</Orders>\n`;
            xml += `      <TaxableAmount>${data.netValue.toFixed(2)}</TaxableAmount>\n`;
            xml += `      <VATAmount>${data.vatAmount.toFixed(2)}</VATAmount>\n`;
            xml += '    </VATReturn>\n';
        });
        
        xml += '  </VATReturns>\n';
        xml += '</IOSSReport>';
        
        return xml;
    }

    generateEstonianVATXML() {
        const [year, month] = this.companyInfo.period.split('-');

        let xml = '<?xml version="1.0" encoding="UTF-8"?>';
        xml += '<ReturnsInformations xsi:noNamespaceSchemaLocation="OSS%20IOSS%20kliendifaili%20formaat.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">';
        xml += '<ReturnsInformation>';
        xml += '<SchemaType>IMPORT</SchemaType>';
        xml += '<TraderID>';
        xml += `<IOSSNumber issuedBy="EE">${this.companyInfo.iossNumber}</IOSSNumber>`;
        xml += '</TraderID>';
        xml += '<Period>';
        xml += `<Year>${year}</Year>`;
        xml += `<Month>${month.padStart(2, '0')}</Month>`;
        xml += '</Period>';

        // Sort by country code for consistent output
        const sortedBreakdown = Object.values(this.processedData.breakdown)
            .sort((a, b) => a.country.localeCompare(b.country));

        sortedBreakdown.forEach(data => {
            // Only include entries with significant amounts
            if (data.netValue > 0.01 && data.vatAmount > 0.01) {
                xml += '<VATReturn>';
                xml += `<MSCONCountryCode>${data.country}</MSCONCountryCode>`;
                xml += '<SupplyType>GOODS</SupplyType>';
                xml += `<VATRate type="STANDARD">${data.vatRate.toFixed(2)}</VATRate>`;
                xml += `<TaxableAmount>${data.netValue.toFixed(2)}</TaxableAmount>`;
                xml += `<VATAmount>${data.vatAmount.toFixed(2)}</VATAmount>`;
                xml += '</VATReturn>';
            }
        });

        xml += '</ReturnsInformation>';
        xml += '</ReturnsInformations>';

        return xml;
    }

    generateRefundsCSV() {
        let csv = 'OrderID,Country,VATRate,RefundAmount,OriginalPeriod,RefundDate,Currency\n';
        
        this.processedRefunds.eligibleRefunds.forEach(refund => {
            const orderId = refund['Order ID'] || refund.Name || 'Unknown';
            const country = this.extractCountry(refund);
            const vatRate = this.extractVATRate(refund);
            const refundAmount = Math.abs(parseFloat(refund['Tax amount'] || refund['Tax Amount'] || 0));
            const originalPeriod = refund.Day?.substring(0, 7) || 'Unknown';
            const refundDate = refund.Day || refund.Date || 'Unknown';
            
            csv += `${orderId},${country},${vatRate},${refundAmount.toFixed(2)},${originalPeriod},${refundDate},EUR\n`;
        });
        
        return csv;
    }

    generateRefundsXML() {
        let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
        xml += '<RefundsReport>\n';
        xml += `  <CompanyInfo>\n`;
        xml += `    <Name>${this.escapeXML(this.companyInfo.name)}</Name>\n`;
        xml += `    <IOSSNumber>${this.companyInfo.iossNumber}</IOSSNumber>\n`;
        xml += `  </CompanyInfo>\n`;
        xml += `  <GeneratedDate>${new Date().toISOString()}</GeneratedDate>\n`;
        xml += '  <Refunds>\n';
        
        this.processedRefunds.eligibleRefunds.forEach(refund => {
            xml += '    <Refund>\n';
            xml += `      <OrderID>${this.escapeXML(refund['Order ID'] || refund.Name || 'Unknown')}</OrderID>\n`;
            xml += `      <Country>${this.extractCountry(refund)}</Country>\n`;
            xml += `      <VATRate>${this.extractVATRate(refund)}</VATRate>\n`;
            xml += `      <RefundAmount>${Math.abs(parseFloat(refund['Tax amount'] || refund['Tax Amount'] || 0)).toFixed(2)}</RefundAmount>\n`;
            xml += `      <OriginalPeriod>${refund.Day?.substring(0, 7) || 'Unknown'}</OriginalPeriod>\n`;
            xml += `      <RefundDate>${refund.Day || refund.Date || 'Unknown'}</RefundDate>\n`;
            xml += '    </Refund>\n';
        });
        
        xml += '  </Refunds>\n';
        xml += '</RefundsReport>';
        
        return xml;
    }

    generateRefundsDetailedReport() {
        let report = `IOSS Refunds Detailed Report\n`;
        report += `${'='.repeat(50)}\n`;
        report += `Generated: ${new Date().toISOString()}\n`;
        report += `Company: ${this.companyInfo.name}\n`;
        report += `IOSS Number: ${this.companyInfo.iossNumber}\n\n`;
        
        const totalRefundAmount = this.processedRefunds.eligibleRefunds.reduce((sum, r) => 
            sum + Math.abs(parseFloat(r['Tax amount'] || r['Tax Amount'] || 0)), 0);
        
        report += `REFUNDS SUMMARY\n`;
        report += `${'='.repeat(30)}\n`;
        report += `Total Refunds Found: ${this.processedRefunds.allRefunds.length}\n`;
        report += `IOSS Eligible Refunds: ${this.processedRefunds.eligibleRefunds.length}\n`;
        report += `Total Refund Amount: €${totalRefundAmount.toFixed(2)}\n`;
        report += `Periods Covered: ${this.processedRefunds.periods.length}\n`;
        report += `Periods: ${this.processedRefunds.periods.join(', ')}\n\n`;
        
        report += `DETAILED REFUNDS LIST\n`;
        report += `${'='.repeat(30)}\n`;
        this.processedRefunds.eligibleRefunds.forEach(refund => {
            const orderId = refund['Order ID'] || refund.Name || 'Unknown';
            const amount = Math.abs(parseFloat(refund['Tax amount'] || refund['Tax Amount'] || 0));
            const country = this.extractCountry(refund);
            const period = refund.Day?.substring(0, 7) || 'Unknown';
            
            report += `Order: ${orderId}, Country: ${country}, Amount: €${amount.toFixed(2)}, Period: ${period}\n`;
        });
        
        return report;
    }

    // Utility functions
    escapeXML(text) {
        return text.toString()
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }

    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // UI helper functions
    updateElement(id, content) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = content;
        }
    }

    getElementValue(id) {
        const element = document.getElementById(id);
        return element ? element.value : '';
    }

    showElement(id) {
        const element = document.getElementById(id);
        if (element) {
            element.style.display = 'block';
        }
    }

    hideElement(id) {
        const element = document.getElementById(id);
        if (element) {
            element.style.display = 'none';
        }
    }

    showProcessingStatus(message) {
        const statusTextElement = document.getElementById('status-text');
        const spinnerElement = document.querySelector('#processing-status .spinner');
        const resultsSection = document.getElementById('results-section');

        if (statusTextElement) {
            statusTextElement.textContent = message;
        }
        if (spinnerElement) {
            spinnerElement.style.display = 'block';
        }
        if (resultsSection) {
            resultsSection.style.display = 'block';
        }
    }

    hideProcessingStatus() {
        const spinnerElement = document.querySelector('#processing-status .spinner');
        const resultsContent = document.getElementById('results-content');

        if (spinnerElement) {
            spinnerElement.style.display = 'none';
        }
        if (resultsContent) {
            resultsContent.style.display = 'block';
        }
    }

    showRefundsProcessingStatus(message) {
        const statusTextElement = document.getElementById('refunds-status-text');
        const spinnerElement = document.querySelector('#refunds-processing-status .spinner');
        const resultsSection = document.getElementById('refunds-results-section');

        if (statusTextElement) {
            statusTextElement.textContent = message;
        }
        if (spinnerElement) {
            spinnerElement.style.display = 'block';
        }
        if (resultsSection) {
            resultsSection.style.display = 'block';
        }
    }

    hideRefundsProcessingStatus() {
        const spinnerElement = document.querySelector('#refunds-processing-status .spinner');
        const resultsContent = document.getElementById('refunds-results-content');

        if (spinnerElement) {
            spinnerElement.style.display = 'none';
        }
        if (resultsContent) {
            resultsContent.style.display = 'block';
        }
    }

    addIssue(type, message) {
        this.issues.push({ type, message, timestamp: new Date().toISOString() });
        console.log(`[${type.toUpperCase()}] ${message}`);
    }

    displayIssues() {
        const issuesContainer = document.getElementById('processing-issues');
        if (!issuesContainer) return;

        if (this.issues.length === 0) {
            issuesContainer.innerHTML = '<p class="success">✅ No issues found during processing</p>';
            return;
        }

        issuesContainer.innerHTML = '';
        this.issues.forEach(issue => {
            const issueElement = document.createElement('div');
            issueElement.className = `issue-item ${issue.type}`;
            issueElement.innerHTML = `
                <span class="issue-type">[${issue.type.toUpperCase()}]</span>
                <span class="issue-message">${issue.message}</span>
            `;
            issuesContainer.appendChild(issueElement);
        });

        issuesContainer.style.display = 'block';
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    console.log('🌟 Starting IOSS Reporter v2.0...');
    window.iossReporter = new IOSSReporter();
});

// Expose for debugging
if (typeof window !== 'undefined') {
    window.IOSSReporter = IOSSReporter;
}
