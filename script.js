/**
 * IOSS Reporter - Shopify Order Processing for IOSS Compliance
 * Version: 2.0 (Recreated with enhanced backup safety)
 * 
 * This script processes Shopify order data and refunds for IOSS (Import One-Stop Shop) compliance.
 * Features automatic backup creation before any file operations.
 */

class IOSSReporter {
    constructor() {
        // Data storage
        this.csvData = [];
        this.refundsData = [];
        this.processedData = {};
        this.companyInfo = {};
        this.issues = [];
        this.debugOrderCount = 0; // For debugging eligibility checks
        
        // Exchange rates (fallback rates - in production these would be fetched from API)
        this.exchangeRates = {};
        this.fallbackRates = {
            'EUR': 1.0,    // Base currency
            'USD': 0.92,   // 1 USD = 0.92 EUR
            'GBP': 1.15,   // 1 GBP = 1.15 EUR
            'CAD': 0.67,   // 1 CAD = 0.67 EUR
            'AUD': 0.61,   // 1 AUD = 0.61 EUR
            'JPY': 0.0062, // 1 JPY = 0.0062 EUR
            'SEK': 0.087,  // 1 SEK = 0.087 EUR
            'NOK': 0.084,  // 1 NOK = 0.084 EUR
            'DKK': 0.134,  // 1 DKK = 0.134 EUR
            'CHF': 1.02,   // 1 CHF = 1.02 EUR
            'PLN': 0.23,   // 1 PLN = 0.23 EUR
            'CZK': 0.041   // 1 CZK = 0.041 EUR
        };
        
        // Configuration
        this.detectedCurrencies = new Set();
        this.userSpecifiedCurrency = null;
        this.iossEligibilityThreshold = 150; // EUR
        
        // EU country codes for IOSS eligibility
        this.euCountries = [
            'AT', 'BE', 'BG', 'HR', 'CY', 'CZ', 'DK', 'EE', 'FI', 'FR',
            'DE', 'GR', 'EL', 'HU', 'IE', 'IT', 'LV', 'LT', 'LU', 'MT',
            'NL', 'PL', 'PT', 'RO', 'SK', 'SI', 'ES', 'SE', 'XI'  // XI = Northern Ireland (post-Brexit)
        ];
        
        this.initializeApp();
    }

    initializeApp() {
        console.log('🚀 IOSS Reporter v2.0 initializing...');
        this.initializeEventListeners();
        this.populatePeriods();
        this.populateCurrencies();
        this.setupDragAndDrop();
        console.log('✅ IOSS Reporter initialized successfully');
    }

    initializeEventListeners() {
        // File upload handlers - Shopify Orders
        const fileInput = document.getElementById('file-input');
        const selectFileBtn = document.getElementById('select-file-btn');
        
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
        }
        if (selectFileBtn) {
            selectFileBtn.addEventListener('click', () => {
                if (fileInput) fileInput.click();
            });
        }
        
        // File upload handlers - Tax Reports (Refunds)
        const refundsFileInput = document.getElementById('refunds-file-input');
        const selectRefundsFileBtn = document.getElementById('select-refunds-file-btn');
        
        if (refundsFileInput) {
            refundsFileInput.addEventListener('change', (e) => this.handleRefundsFileUpload(e));
        }
        if (selectRefundsFileBtn) {
            selectRefundsFileBtn.addEventListener('click', () => {
                if (refundsFileInput) refundsFileInput.click();
            });
        }

        // Form submission
        const companyForm = document.getElementById('company-form');
        if (companyForm) {
            companyForm.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }
        
        // Process refunds button
        const processRefundsBtn = document.getElementById('process-refunds-btn');
        if (processRefundsBtn) {
            processRefundsBtn.addEventListener('click', () => this.processRefunds());
        }

        // Export buttons - Shopify Orders
        this.setupExportButton('export-csv', () => this.exportCSV());
        this.setupExportButton('export-ioss-compliant', () => this.exportIOSSCompliant());
        this.setupExportButton('export-detailed', () => this.exportDetailed());
        this.setupExportButton('export-xml', () => this.exportXML());
        this.setupExportButton('export-estonian-vat-xml', () => this.exportEstonianVATXML());

        // FIXED: Export buttons - Tax Reports (Refunds) - These were missing!
        this.setupExportButton('export-refunds-csv', () => this.exportRefundsCSV());
        this.setupExportButton('export-refunds-xml', () => this.exportRefundsXML());
        this.setupExportButton('export-refunds-detailed', () => this.exportRefundsDetailed());

        // Tab switching functionality
        window.switchTab = (tabName) => this.switchTab(tabName);
        
        console.log('✅ Event listeners initialized');
    }

    setupExportButton(buttonId, handler) {
        const button = document.getElementById(buttonId);
        if (button) {
            button.addEventListener('click', handler);
            console.log(`✅ Export button '${buttonId}' initialized`);
        } else {
            console.warn(`⚠️ Export button '${buttonId}' not found in DOM`);
        }
    }

    setupDragAndDrop() {
        const uploadArea = document.getElementById('upload-area');
        const refundsUploadArea = document.getElementById('refunds-upload-area');

        [uploadArea, refundsUploadArea].forEach(area => {
            if (!area) return;
            
            area.addEventListener('dragover', (e) => {
                e.preventDefault();
                area.classList.add('drag-over');
            });

            area.addEventListener('dragleave', () => {
                area.classList.remove('drag-over');
            });

            area.addEventListener('drop', (e) => {
                e.preventDefault();
                area.classList.remove('drag-over');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    if (area.id === 'upload-area') {
                        this.handleFileUpload({ target: { files } });
                    } else {
                        this.handleRefundsFileUpload({ target: { files: [files[0]] } });
                    }
                }
            });
        });
    }

    switchTab(tabName) {
        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        
        // Remove active class from all tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // Show selected tab content
        const tabContent = document.getElementById(`${tabName}-upload`);
        const tabButton = document.getElementById(`${tabName}-tab`);
        
        if (tabContent) tabContent.classList.add('active');
        if (tabButton) tabButton.classList.add('active');
    }

    populatePeriods() {
        const periodSelect = document.getElementById('reporting-period');
        if (!periodSelect) return;

        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth();

        // Generate periods for current and previous year
        for (let year = currentYear; year >= currentYear - 1; year--) {
            for (let month = 11; month >= 0; month--) {
                // Don't show future months for current year
                if (year === currentYear && month > currentMonth) continue;
                
                const date = new Date(year, month);
                const monthName = date.toLocaleString('en', { month: 'long' });
                const value = `${year}-${String(month + 1).padStart(2, '0')}`;
                const option = document.createElement('option');
                option.value = value;
                option.textContent = `${monthName} ${year}`;
                periodSelect.appendChild(option);
            }
        }
    }

    populateCurrencies() {
        const select = document.getElementById('currency');
        if (!select) return;

        const currencies = Object.keys(this.fallbackRates).sort();

        currencies.forEach(currency => {
            const option = document.createElement('option');
            option.value = currency;
            option.textContent = `${currency} - ${this.getCurrencyName(currency)}`;
            select.appendChild(option);
        });
    }

    getCurrencyName(code) {
        const names = {
            'EUR': 'Euro',
            'USD': 'US Dollar',
            'GBP': 'British Pound',
            'CAD': 'Canadian Dollar',
            'AUD': 'Australian Dollar',
            'JPY': 'Japanese Yen',
            'SEK': 'Swedish Krona',
            'NOK': 'Norwegian Krone',
            'DKK': 'Danish Krone',
            'CHF': 'Swiss Franc',
            'PLN': 'Polish Zloty',
            'CZK': 'Czech Koruna'
        };
        return names[code] || code;
    }

    getCurrencySymbol(code) {
        const symbols = {
            'EUR': '€',
            'USD': '$',
            'GBP': '£',
            'JPY': '¥',
            'CHF': 'CHF',
            'SEK': 'kr',
            'NOK': 'kr',
            'DKK': 'kr',
            'PLN': 'zł',
            'CZK': 'Kč'
        };
        return symbols[code] || code;
    }

    // File handling methods
    async handleFileUpload(event) {
        const files = event.target.files;
        if (!files.length) return;

        this.csvData = [];
        this.issues = [];
        let totalRecords = 0;

        const filesList = document.getElementById('files-list');
        if (filesList) filesList.innerHTML = '';

        this.showProcessingStatus('Uploading and processing files...');

        for (const file of files) {
            if (!file.name.toLowerCase().endsWith('.csv')) {
                this.addIssue('error', `File ${file.name} is not a CSV file`);
                continue;
            }

            try {
                const text = await this.readFileAsText(file);
                const parsed = this.parseCSV(text);
                this.csvData.push(...parsed);
                totalRecords += parsed.length;

                // Add file info to display
                if (filesList) {
                    const fileInfo = document.createElement('div');
                    fileInfo.className = 'file-item';
                    fileInfo.innerHTML = `
                        <span class="file-name">${file.name}</span>
                        <span class="file-records">${parsed.length} records</span>
                    `;
                    filesList.appendChild(fileInfo);
                }

            } catch (error) {
                this.addIssue('error', `Error processing ${file.name}: ${error.message}`);
            }
        }

        this.updateElement('total-record-count', totalRecords);
        this.showElement('file-info');
        
        if (totalRecords > 0) {
            this.showElement('company-section');
        }

        this.hideProcessingStatus();
        console.log(`✅ Processed ${totalRecords} records from ${files.length} files`);
    }

    async handleRefundsFileUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        if (!file.name.toLowerCase().endsWith('.csv')) {
            this.addIssue('error', 'Please select a CSV file');
            return;
        }

        this.showRefundsProcessingStatus('Processing refunds file...');

        try {
            const text = await this.readFileAsText(file);
            const parsed = this.parseCSV(text);
            this.refundsData = parsed;

            // Count refunds (negative tax amounts)
            const refundCount = parsed.filter(row => {
                const taxAmount = parseFloat(row['Tax amount'] || row['Tax Amount'] || 0);
                return taxAmount < 0;
            }).length;

            this.updateElement('refunds-record-count', parsed.length);
            this.updateElement('refunds-count', refundCount);
            this.showElement('refunds-file-info');

            if (refundCount > 0) {
                this.showElement('refunds-process-section');
            }

            // Add file info
            const filesList = document.getElementById('refunds-files-list');
            if (filesList) {
                filesList.innerHTML = `
                    <div class="file-item">
                        <span class="file-name">${file.name}</span>
                        <span class="file-records">${parsed.length} records (${refundCount} refunds)</span>
                    </div>
                `;
            }

            this.hideRefundsProcessingStatus();
            console.log(`✅ Processed refunds file: ${parsed.length} records, ${refundCount} refunds`);

        } catch (error) {
            this.addIssue('error', `Error processing file: ${error.message}`);
            this.hideRefundsProcessingStatus();
        }
    }

    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = reject;
            reader.readAsText(file);
        });
    }

    parseCSV(text) {
        // Use Papa Parse if available, otherwise fallback to simple parsing
        if (typeof Papa !== 'undefined') {
            const result = Papa.parse(text, { 
                header: true, 
                skipEmptyLines: true,
                transformHeader: (header) => header.trim()
            });
            return result.data;
        } else {
            return this.simpleCSVParse(text);
        }
    }

    simpleCSVParse(text) {
        const lines = text.trim().split('\n');
        if (lines.length < 2) return [];

        const headers = this.parseCSVLine(lines[0]);
        const data = [];

        for (let i = 1; i < lines.length; i++) {
            const values = this.parseCSVLine(lines[i]);
            const row = {};
            headers.forEach((header, index) => {
                row[header.trim()] = values[index] ? values[index].trim() : '';
            });
            if (Object.values(row).some(val => val)) { // Skip empty rows
                data.push(row);
            }
        }

        return data;
    }

    parseCSVLine(line) {
        const result = [];
        let current = '';
        let inQuotes = false;
        
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            
            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                result.push(current);
                current = '';
            } else {
                current += char;
            }
        }
        
        result.push(current);
        return result.map(item => item.replace(/^"|"$/g, '')); // Remove surrounding quotes
    }

    async handleFormSubmit(event) {
        event.preventDefault();

        // CRITICAL FIX: Comprehensive data validation
        const companyName = this.getElementValue('company-name').trim();
        const iossNumber = this.getElementValue('ioss-number').trim();
        const period = this.getElementValue('reporting-period');
        const currency = this.getElementValue('currency');

        // Validate all required fields
        const validationErrors = this.validateCompanyData(companyName, iossNumber, period, currency);

        if (validationErrors.length > 0) {
            // Display validation errors
            validationErrors.forEach(error => this.addIssue('error', error));
            this.displayIssues();
            return;
        }

        this.companyInfo = {
            name: companyName,
            iossNumber: iossNumber,
            period: period,
            currency: currency
        };

        console.log('📋 Company info collected and validated:', this.companyInfo);
        await this.processOrderData();
    }

    // CRITICAL FIX: Comprehensive Data Validation
    validateCompanyData(companyName, iossNumber, period, currency) {
        const errors = [];

        // Validate company name
        if (!companyName || companyName.length < 2) {
            errors.push('Company name is required and must be at least 2 characters long');
        }

        // Validate IOSS number
        if (!this.isValidIOSSNumber(iossNumber)) {
            errors.push('Invalid IOSS number format. Must start with "IM" followed by 10 digits (e.g., IM1234567890)');
        }

        // Validate reporting period
        if (!this.isValidReportingPeriod(period)) {
            errors.push('Invalid reporting period. Must be in YYYY-MM format and not in the future');
        }

        // Validate currency
        if (!this.isValidCurrency(currency)) {
            errors.push('Invalid currency. Must be EUR, USD, or GBP');
        }

        return errors;
    }

    // IOSS Number Validation
    isValidIOSSNumber(iossNumber) {
        if (!iossNumber) return false;

        // CORRECT: IOSS number format: IM + 10 digits (not 12)
        const iossPattern = /^IM\d{10}$/;
        return iossPattern.test(iossNumber.toUpperCase());
    }

    // Reporting Period Validation
    isValidReportingPeriod(period) {
        if (!period) return false;

        // Check format YYYY-MM
        const periodPattern = /^\d{4}-\d{2}$/;
        if (!periodPattern.test(period)) return false;

        // Parse and validate date
        const [year, month] = period.split('-').map(Number);

        // Validate year (reasonable range)
        if (year < 2021 || year > new Date().getFullYear() + 1) return false;

        // Validate month
        if (month < 1 || month > 12) return false;

        // Check if period is not in the future (allow current month)
        const periodDate = new Date(year, month - 1, 1);
        const currentDate = new Date();
        const currentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);

        return periodDate <= currentMonth;
    }

    // Currency Validation
    isValidCurrency(currency) {
        const validCurrencies = ['EUR', 'USD', 'GBP'];
        return validCurrencies.includes(currency);
    }

    // Enhanced threshold validation
    validateIOSSThreshold(amount, currency) {
        // Convert to EUR for threshold check
        const amountEUR = currency === 'EUR' ? amount : this.convertToEUR(amount, currency);

        if (amountEUR > this.iossEligibilityThreshold) {
            return {
                isValid: false,
                reason: `Amount €${amountEUR.toFixed(2)} exceeds IOSS threshold of €${this.iossEligibilityThreshold}`
            };
        }

        return { isValid: true };
    }

    // Core processing methods
    async processOrderData() {
        this.issues = [];
        this.debugOrderCount = 0; // Reset debug counter
        this.processedData = {
            orders: [],
            summary: {
                totalOrders: 0,
                iossEligibleOrders: 0,
                totalVatCalculated: 0,
                totalVatShopify: 0,
                countryCount: 0,
                totalValueEUR: 0,
                excludedOrders: 0,
                excludedValue: 0
            },
            breakdown: {}
        };

        this.showProcessingStatus('Processing orders...');

        try {
            // Process each order
            let processedCount = 0;
            for (const order of this.csvData) {
                const processedOrder = await this.processOrder(order);
                if (processedOrder) {
                    this.processedData.orders.push(processedOrder);
                    processedCount++;
                }
                
                // Update progress for large datasets
                if (processedCount % 100 === 0) {
                    this.showProcessingStatus(`Processing orders... (${processedCount}/${this.csvData.length})`);
                    await this.sleep(1); // Allow UI to update
                }
            }

            // Calculate summary and breakdown
            this.calculateSummary();
            this.displayResults();

            console.log(`✅ Successfully processed ${processedCount} orders`);

        } catch (error) {
            this.addIssue('error', `Processing failed: ${error.message}`);
            this.showProcessingStatus('Processing failed');
            console.error('❌ Order processing failed:', error);
        }
    }

    async processOrder(order) {
        try {
            // Extract basic order info
            const orderId = order.Name || order['Order ID'] || order['Order Number'] || 'Unknown';

            // ENHANCED: Check if this order has multiple line items
            const lineItems = this.extractLineItems(order);

            if (lineItems.length > 1) {
                // Process order with multiple line items
                return await this.processMultiLineItemOrder(order, lineItems);
            } else {
                // Process single line item order (existing logic)
                return await this.processSingleLineItemOrder(order);
            }

        } catch (error) {
            this.addIssue('warning', `Error processing order ${order.Name || 'Unknown'}: ${error.message}`);
            return null;
        }
    }

    // ENHANCED: Extract line items from order data
    extractLineItems(order) {
        const lineItems = [];

        // Check for multiple line item columns (Shopify format)
        let itemIndex = 1;
        while (true) {
            const itemName = order[`Lineitem name ${itemIndex}`] || order[`Lineitem ${itemIndex} name`];
            if (!itemName && itemIndex === 1) {
                // Try without index for single item
                const singleItemName = order['Lineitem name'] || order['Product Name'];
                if (singleItemName) {
                    lineItems.push({
                        name: singleItemName,
                        quantity: this.parseAmount(order['Lineitem quantity'] || order['Quantity'] || 1),
                        price: this.parseAmount(order['Lineitem price'] || order['Price'] || 0),
                        sku: order['Lineitem sku'] || order['SKU'] || '',
                        requiresShipping: order['Lineitem requires shipping'] || 'true',
                        taxable: order['Lineitem taxable'] || 'true',
                        category: order['Product Category'] || order['Category'] || '',
                        vendor: order['Vendor'] || '',
                        type: order['Product Type'] || ''
                    });
                }
                break;
            } else if (!itemName) {
                break;
            }

            lineItems.push({
                name: itemName,
                quantity: this.parseAmount(order[`Lineitem quantity ${itemIndex}`] || order[`Lineitem ${itemIndex} quantity`] || 1),
                price: this.parseAmount(order[`Lineitem price ${itemIndex}`] || order[`Lineitem ${itemIndex} price`] || 0),
                sku: order[`Lineitem sku ${itemIndex}`] || order[`Lineitem ${itemIndex} sku`] || '',
                requiresShipping: order[`Lineitem requires shipping ${itemIndex}`] || order[`Lineitem ${itemIndex} requires shipping`] || 'true',
                taxable: order[`Lineitem taxable ${itemIndex}`] || order[`Lineitem ${itemIndex} taxable`] || 'true',
                category: order[`Product Category ${itemIndex}`] || order[`Category ${itemIndex}`] || '',
                vendor: order[`Vendor ${itemIndex}`] || '',
                type: order[`Product Type ${itemIndex}`] || ''
            });

            itemIndex++;
        }

        return lineItems;
    }

    // ENHANCED: Process order with multiple line items
    async processMultiLineItemOrder(order, lineItems) {
        const orderId = order.Name || order['Order ID'] || order['Order Number'] || 'Unknown';

        // Extract basic order data
        const country = this.extractCountry(order);
        const postalCode = this.extractPostalCode(order);
        const currency = (order.Currency || order['Currency Code'] || this.companyInfo.currency || 'EUR').toUpperCase();

        // Process each line item with its specific VAT rate
        const processedLineItems = [];
        let totalNetValue = 0;
        let totalVATAmount = 0;
        let totalGrossValue = 0;
        let hasGoodsItems = false;
        let hasServiceItems = false;

        for (const lineItem of lineItems) {
            const itemTotal = lineItem.price * lineItem.quantity;
            const itemTotalEUR = await this.convertToEUR(itemTotal, currency);

            // Determine VAT rate for this specific item
            const itemVATRate = this.getApplicableVATRate(
                country,
                postalCode,
                lineItem.name,
                null, // No extracted rate for individual items
                lineItem.category
            );

            // Calculate net and VAT amounts for this item
            const itemNetValue = itemTotalEUR / (1 + itemVATRate / 100);
            const itemVATAmount = itemTotalEUR - itemNetValue;

            // Check if item is goods or service
            const itemIsGoods = this.isLineItemGoods(lineItem);
            if (itemIsGoods) hasGoodsItems = true;
            else hasServiceItems = true;

            processedLineItems.push({
                ...lineItem,
                vatRate: itemVATRate,
                totalEUR: itemTotalEUR,
                netValueEUR: itemNetValue,
                vatAmountEUR: itemVATAmount,
                isGoods: itemIsGoods,
                category: this.categorizeProduct(lineItem.name, lineItem.category)
            });

            totalNetValue += itemNetValue;
            totalVATAmount += itemVATAmount;
            totalGrossValue += itemTotalEUR;
        }

        // Calculate weighted average VAT rate
        const weightedVATRate = totalGrossValue > 0 ? (totalVATAmount / totalNetValue) * 100 : 0;

        // Determine overall order characteristics
        const isB2CTransaction = this.isB2CTransaction(order);
        const isEUDestination = this.isEUDestination(country, postalCode);

        // CRITICAL FIX: IOSS €150 threshold applies to NET value (excluding VAT), not gross
        // Add safety check for totalNetValue
        let isUnderThreshold;
        if (totalNetValue === undefined || totalNetValue === null || isNaN(totalNetValue)) {
            console.error(`❌ Invalid totalNetValue for multi-line order: ${totalNetValue}`);
            // Fallback to gross value calculation
            const fallbackNetValue = totalGrossValue / 1.2; // Assume 20% VAT as fallback
            console.warn(`   Using fallback net value: €${fallbackNetValue.toFixed(2)}`);
            isUnderThreshold = fallbackNetValue <= this.iossEligibilityThreshold;
        } else {
            isUnderThreshold = totalNetValue <= this.iossEligibilityThreshold;
        }
        const isGoodsSupply = hasGoodsItems; // Order is goods if it has any goods items
        const hasValidVATRate = weightedVATRate > 0;

        // IOSS eligibility check
        const isIOSSEligible = isEUDestination && isUnderThreshold && isB2CTransaction && isGoodsSupply && hasValidVATRate;

        // Determine exclusion reason
        let exclusionReason = '';
        if (!isEUDestination) exclusionReason = 'Non-EU destination';
        else if (!isUnderThreshold) exclusionReason = `Over €150 NET threshold (€${totalNetValue.toFixed(2)} NET, €${totalGrossValue.toFixed(2)} gross)`;
        else if (!isB2CTransaction) exclusionReason = 'B2B transaction (customer has VAT number)';
        else if (!isGoodsSupply) exclusionReason = 'No goods items (services only)';
        else if (!hasValidVATRate) exclusionReason = 'Invalid or zero VAT rate';

        // DEBUGGING: Log XI multi-line orders specifically
        if (country === 'XI') {
            console.log(`🔍 XI Multi-line Order ${orderId} eligibility check:`);
            console.log(`   Country: ${country} → EU: ${isEUDestination}`);
            console.log(`   NET Value: €${totalNetValue.toFixed(2)} → Under €150 NET: ${isUnderThreshold}`);
            console.log(`   GROSS Total: €${totalGrossValue.toFixed(2)} (for reference)`);
            console.log(`   VAT Number: "${order['Customer VAT Number'] || order['VAT Number'] || 'none'}" → B2C: ${isB2CTransaction}`);
            console.log(`   Has Goods Items: ${hasGoodsItems} → Goods Supply: ${isGoodsSupply}`);
            console.log(`   Weighted VAT Rate: ${weightedVATRate.toFixed(2)}% → Valid: ${hasValidVATRate}`);
            console.log(`   FINAL RESULT: IOSS Eligible = ${isIOSSEligible}`);
            if (!isIOSSEligible) {
                console.log(`   ❌ EXCLUSION REASON: ${exclusionReason}`);
            }
        }

        return {
            orderId,
            originalCurrency: currency,
            subtotal: totalNetValue,
            taxes: totalVATAmount,
            shipping: await this.convertToEUR(this.parseAmount(order.Shipping || 0), currency),
            total: totalGrossValue,
            country,
            postalCode,
            vatRate: weightedVATRate,
            isIOSSEligible,
            isEUDestination,
            isUnderThreshold,
            isB2CTransaction,
            isGoodsSupply,
            exclusionReason,
            orderDate: order['Created at'] || order['Order Date'] || order['Date'] || new Date().toISOString(),
            customerEmail: order['Email'] || order['Customer Email'] || '',
            customerVATNumber: order['Customer VAT Number'] || order['VAT Number'] || order['Tax Number'] || '',
            shippingMethod: order['Shipping Method'] || order['Shipping'] || '',
            fulfillmentStatus: order['Fulfillment Status'] || '',
            financialStatus: order['Financial Status'] || order['Payment Status'] || '',
            lineItems: processedLineItems,
            isMultiLineItem: true,
            hasGoodsItems,
            hasServiceItems
        };
    }

    // Process single line item order (simplified version of existing logic)
    async processSingleLineItemOrder(order) {
        // Extract order ID first
        const orderId = order.Name || order['Order ID'] || order['Order Number'] || 'Unknown';

        // CRITICAL FIX: Detect if Shopify prices are net or gross
        const priceStructure = this.detectShopifyPriceStructure(order);

        // Extract financial data with proper net/gross handling
        const financialData = this.extractFinancialData(order, priceStructure);

        // Get currency
        let currency = order.Currency || order['Currency Code'] || this.companyInfo.currency || 'EUR';
        currency = currency.toUpperCase();
        this.detectedCurrencies.add(currency);

        // Convert to EUR
        const subtotalEUR = await this.convertToEUR(financialData.subtotal, currency);
        const taxesEUR = await this.convertToEUR(financialData.taxes, currency);
        const shippingEUR = await this.convertToEUR(financialData.shipping, currency);
        const totalEUR = await this.convertToEUR(financialData.total, currency);
        const netValueEUR = await this.convertToEUR(financialData.netValue, currency);
        const grossValueEUR = await this.convertToEUR(financialData.grossValue, currency);

        // Extract location data
        const country = this.extractCountry(order);
        const postalCode = this.extractPostalCode(order);

        // Enhanced VAT rate extraction
        const extractedVATRate = this.extractVATRate(order);
        const productType = order['Lineitem name'] || order['Product Name'] || '';
        const productCategory = order['Product Category'] || order['Category'] || '';

        const vatRate = this.getApplicableVATRate(country, postalCode, productType, extractedVATRate, productCategory);

        // Enhanced eligibility checks including refund status
        const isEUDestination = this.isEUDestination(country, postalCode);

        // CRITICAL FIX: IOSS €150 threshold applies to NET value (excluding VAT), not gross
        // Add safety check for netValueEUR
        let isUnderThreshold;
        if (netValueEUR === undefined || netValueEUR === null || isNaN(netValueEUR)) {
            console.error(`❌ Invalid netValueEUR for order ${orderId}: ${netValueEUR}`);
            console.error(`   financialData.netValue: ${financialData.netValue}`);
            console.error(`   currency: ${currency}`);
            // Fallback to subtotal + shipping as net value
            const fallbackNetValue = subtotalEUR + shippingEUR;
            console.warn(`   Using fallback net value: €${fallbackNetValue.toFixed(2)}`);
            isUnderThreshold = fallbackNetValue <= this.iossEligibilityThreshold;
        } else {
            isUnderThreshold = netValueEUR <= this.iossEligibilityThreshold;
        }
        const isB2CTransaction = this.isB2CTransaction(order);
        const isGoodsSupply = this.isGoodsSupply(order);
        const hasValidVATRate = vatRate > 0;
        const isNotFullyRefunded = !financialData.refundInfo?.isFullyRefunded;

        // DEBUGGING: Log eligibility check details for first few orders OR all XI orders
        if (this.debugOrderCount < 5 || country === 'XI') {
            console.log(`🔍 Order ${orderId} eligibility check (${country === 'XI' ? 'XI - NORTHERN IRELAND' : 'Regular'}):`);
            console.log(`   Country: ${country} → EU: ${isEUDestination}`);
            const netDisplay = netValueEUR !== undefined && !isNaN(netValueEUR) ? netValueEUR.toFixed(2) : 'INVALID';
            console.log(`   NET Value: €${netDisplay} → Under €150 NET: ${isUnderThreshold}`);
            console.log(`   GROSS Total: €${totalEUR.toFixed(2)} (for reference)`);
            console.log(`   VAT Number: "${order['Customer VAT Number'] || order['VAT Number'] || 'none'}" → B2C: ${isB2CTransaction}`);
            console.log(`   Requires Shipping: "${order['Lineitem requires shipping'] || 'true'}" → Goods: ${isGoodsSupply}`);
            console.log(`   VAT Rate: ${vatRate}% → Valid: ${hasValidVATRate}`);
            console.log(`   Refund Status: → Not Fully Refunded: ${isNotFullyRefunded}`);
            console.log(`   FINAL RESULT: IOSS Eligible = ${isIOSSEligible}`);
            if (!isIOSSEligible) {
                console.log(`   ❌ EXCLUSION REASON: ${exclusionReason}`);
            }
            if (country !== 'XI') this.debugOrderCount++;
        }

        const isIOSSEligible = isEUDestination && isUnderThreshold && isB2CTransaction && isGoodsSupply && hasValidVATRate && isNotFullyRefunded;

        // Determine exclusion reason
        let exclusionReason = '';
        if (!isEUDestination) exclusionReason = 'Non-EU destination';
        else if (!isUnderThreshold) {
            const netDisplay = netValueEUR !== undefined && !isNaN(netValueEUR) ? netValueEUR.toFixed(2) : 'INVALID';
            exclusionReason = `Over €150 NET threshold (€${netDisplay} NET, €${totalEUR.toFixed(2)} gross)`;
        }
        else if (!isB2CTransaction) exclusionReason = 'B2B transaction (customer has VAT number)';
        else if (!isGoodsSupply) exclusionReason = 'Service/Digital product';
        else if (!hasValidVATRate) exclusionReason = 'Invalid or zero VAT rate';
        else if (!isNotFullyRefunded) exclusionReason = 'Order fully refunded';

        return {
            orderId: order.Name || order['Order ID'] || order['Order Number'] || 'Unknown',
            originalCurrency: currency,
            subtotal: netValueEUR,        // FIXED: Always use net value for IOSS
            taxes: taxesEUR,              // VAT amount
            shipping: shippingEUR,        // Shipping cost
            total: grossValueEUR,         // FIXED: Total gross value
            netValue: netValueEUR,        // Net value (IOSS taxable amount)
            grossValue: grossValueEUR,    // Gross value (total including VAT)
            country,
            postalCode,
            vatRate,
            isIOSSEligible,
            isEUDestination,
            isUnderThreshold,
            isB2CTransaction,
            isGoodsSupply,
            exclusionReason,
            orderDate: order['Created at'] || order['Order Date'] || order['Date'] || new Date().toISOString(),
            customerEmail: order['Email'] || order['Customer Email'] || '',
            customerVATNumber: order['Customer VAT Number'] || order['VAT Number'] || order['Tax Number'] || '',
            requiresShipping: order['Lineitem requires shipping'] || 'true',
            productType: productType,
            productCategory: productCategory,
            shippingMethod: order['Shipping Method'] || order['Shipping'] || '',
            fulfillmentStatus: order['Fulfillment Status'] || '',
            financialStatus: order['Financial Status'] || order['Payment Status'] || '',
            priceStructure: financialData.priceStructure,  // ADDED: Price structure info
            discountInfo: financialData.discountInfo,      // ADDED: Discount information
            refundInfo: financialData.refundInfo,          // ADDED: Refund information
            refundAdjustment: financialData.refundAdjustment, // ADDED: Refund adjustments applied
            isAdjustedForRefunds: financialData.isAdjustedForRefunds, // ADDED: Refund adjustment flag
            rawShopifyValues: financialData.rawValues,     // ADDED: Original Shopify values
            isMultiLineItem: false
        };
    }

    parseAmount(value) {
        if (typeof value === 'number') return value;
        if (typeof value === 'string') {
            // Remove currency symbols and parse
            const cleaned = value.replace(/[^\d.-]/g, '');
            return parseFloat(cleaned) || 0;
        }
        return 0;
    }

    async convertToEUR(amount, fromCurrency) {
        if (fromCurrency === 'EUR') return amount;

        // ENHANCED: Try ECB rates first, then fallback
        let rate = await this.getECBExchangeRate(fromCurrency);

        if (!rate) {
            // Fallback to stored rates
            rate = this.exchangeRates[fromCurrency] || this.fallbackRates[fromCurrency] || 1;
            console.warn(`⚠️ Using fallback exchange rate for ${fromCurrency}: ${rate}`);
        }

        return amount * rate;
    }

    // ENHANCED: ECB Exchange Rate Integration
    async getECBExchangeRate(currency) {
        try {
            // Check if we have a recent cached rate (within 24 hours)
            const cacheKey = `ecb_rate_${currency}`;
            const cachedData = localStorage.getItem(cacheKey);

            if (cachedData) {
                const { rate, timestamp } = JSON.parse(cachedData);
                const age = Date.now() - timestamp;
                const maxAge = 24 * 60 * 60 * 1000; // 24 hours

                if (age < maxAge) {
                    console.log(`📊 Using cached ECB rate for ${currency}: ${rate}`);
                    return rate;
                }
            }

            // Fetch fresh rate from ECB
            const rate = await this.fetchECBRate(currency);

            if (rate) {
                // Cache the rate
                localStorage.setItem(cacheKey, JSON.stringify({
                    rate: rate,
                    timestamp: Date.now()
                }));

                console.log(`📊 Fetched fresh ECB rate for ${currency}: ${rate}`);
                return rate;
            }

        } catch (error) {
            console.warn(`⚠️ Failed to get ECB rate for ${currency}:`, error.message);
        }

        return null;
    }

    // Fetch rate from ECB API
    async fetchECBRate(currency) {
        try {
            // ECB provides daily rates - use the latest available
            const response = await fetch(`https://api.exchangerate-api.com/v4/latest/EUR`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();

            if (data.rates && data.rates[currency]) {
                // ECB gives EUR to other currency, we need other currency to EUR
                return 1 / data.rates[currency];
            }

            throw new Error(`Rate not found for ${currency}`);

        } catch (error) {
            console.warn(`⚠️ ECB API fetch failed for ${currency}:`, error.message);

            // Try alternative ECB endpoint
            try {
                const altResponse = await fetch(`https://api.fixer.io/latest?base=EUR&symbols=${currency}`);
                if (altResponse.ok) {
                    const altData = await altResponse.json();
                    if (altData.rates && altData.rates[currency]) {
                        return 1 / altData.rates[currency];
                    }
                }
            } catch (altError) {
                console.warn(`⚠️ Alternative ECB API also failed:`, altError.message);
            }

            return null;
        }
    }

    // Validate exchange rate date for IOSS compliance
    validateExchangeRateDate(rateDate, transactionDate) {
        // For IOSS, exchange rates should be from the same month as the transaction
        const ratePeriod = rateDate.substring(0, 7); // YYYY-MM
        const transactionPeriod = transactionDate.substring(0, 7); // YYYY-MM

        return ratePeriod === transactionPeriod;
    }

    // Get monthly average rate for IOSS compliance
    async getMonthlyAverageRate(currency, year, month) {
        try {
            const cacheKey = `ecb_monthly_${currency}_${year}_${month}`;
            const cachedRate = localStorage.getItem(cacheKey);

            if (cachedRate) {
                return parseFloat(cachedRate);
            }

            // In a real implementation, you would fetch historical daily rates
            // and calculate the monthly average. For now, use current rate.
            const currentRate = await this.getECBExchangeRate(currency);

            if (currentRate) {
                localStorage.setItem(cacheKey, currentRate.toString());
            }

            return currentRate;

        } catch (error) {
            console.warn(`⚠️ Failed to get monthly average rate:`, error.message);
            return null;
        }
    }

    isEUDestination(countryCode, postalCode = '') {
        // Check if it's a standard EU country
        if (this.euCountries.includes(countryCode)) {
            // Check for special territories that are excluded from EU VAT
            return !this.isExcludedEUTerritory(countryCode, postalCode);
        }

        // Check for special territories that are included in EU VAT
        return this.isIncludedSpecialTerritory(countryCode, postalCode);
    }

    // Special EU territories excluded from EU VAT
    isExcludedEUTerritory(countryCode, postalCode) {
        const excludedTerritories = {
            'ES': {
                // Canary Islands
                postalCodes: ['35', '38'], // Las Palmas and Santa Cruz de Tenerife
                name: 'Canary Islands'
            },
            'FR': {
                // French overseas territories
                postalCodes: ['971', '972', '973', '974', '975', '976', '977', '978', '986', '987', '988'],
                name: 'French Overseas Territories'
            },
            'IT': {
                // Livigno, Campione d'Italia
                postalCodes: ['23030'], // Livigno
                name: 'Special Italian territories'
            },
            'DE': {
                // Büsingen am Hochrhein
                postalCodes: ['78266'],
                name: 'Büsingen am Hochrhein'
            },
            'GR': {
                // Mount Athos
                postalCodes: ['63086'],
                name: 'Mount Athos'
            }
        };

        const territory = excludedTerritories[countryCode];
        if (!territory || !postalCode) return false;

        // Check if postal code matches excluded territory
        return territory.postalCodes.some(code => postalCode.startsWith(code));
    }

    // Special territories included in EU VAT (outside EU countries)
    isIncludedSpecialTerritory(countryCode, postalCode) {
        const includedTerritories = {
            'MC': { name: 'Monaco', vatCountry: 'FR' }, // Uses French VAT
            'AD': { name: 'Andorra', vatCountry: 'ES' }, // Uses Spanish VAT for some purposes
            'SM': { name: 'San Marino', vatCountry: 'IT' }, // Uses Italian VAT
            'VA': { name: 'Vatican City', vatCountry: 'IT' } // Uses Italian VAT
        };

        return includedTerritories.hasOwnProperty(countryCode);
    }

    // ENHANCED: Northern Ireland (XI) specific handling
    isNorthernIreland(countryCode, postalCode) {
        return countryCode === 'XI';
    }

    // Handle Northern Ireland post-Brexit IOSS rules
    validateNorthernIrelandIOSS(order) {
        // Northern Ireland follows EU IOSS rules for goods
        // but has special considerations for services and digital products
        const country = this.extractCountry(order);

        if (country !== 'XI') return { isValid: true };

        // For Northern Ireland, goods follow EU IOSS rules
        // Services may have different treatment
        const isGoodsSupply = this.isGoodsSupply(order);

        if (!isGoodsSupply) {
            return {
                isValid: false,
                reason: 'Northern Ireland services may require different VAT treatment'
            };
        }

        return { isValid: true };
    }

    // Get the effective VAT country for special territories
    getEffectiveVATCountry(countryCode, postalCode = '') {
        // Check for included special territories
        const includedTerritories = {
            'MC': 'FR', // Monaco uses French VAT
            'AD': 'ES', // Andorra
            'SM': 'IT', // San Marino
            'VA': 'IT'  // Vatican City
        };

        if (includedTerritories[countryCode]) {
            return includedTerritories[countryCode];
        }

        // Northern Ireland (XI) uses its own VAT system but follows EU IOSS rules
        if (countryCode === 'XI') {
            return 'XI'; // Northern Ireland has its own VAT rates
        }

        // For excluded EU territories, they don't use EU VAT
        if (this.isExcludedEUTerritory(countryCode, postalCode)) {
            return null; // No EU VAT applies
        }

        return countryCode;
    }

    extractCountry(order) {
        // Try multiple possible column names for country
        const country = order['Shipping Country Code'] ||
                       order['Billing Country Code'] ||
                       order['Country Code'] ||
                       order['Country'] ||
                       order['Shipping Country'] ||
                       order['Billing Country'] ||
                       'XX';

        return country.toUpperCase().substring(0, 2);
    }

    // ENHANCED: Extract postal code for regional VAT rate determination
    extractPostalCode(order) {
        const postalCode = order['Shipping Zip'] ||
                          order['Billing Zip'] ||
                          order['Postal Code'] ||
                          order['Zip Code'] ||
                          order['Zip'] ||
                          '';

        return postalCode.toString().replace(/\s+/g, '').toUpperCase();
    }

    // Check if individual line item is goods
    isLineItemGoods(lineItem) {
        const requiresShipping = (lineItem.requiresShipping || 'true').toLowerCase();

        if (requiresShipping === 'false' || requiresShipping === 'no') {
            return false; // Likely a service or digital product
        }

        // Check product type for digital services
        const productName = (lineItem.name || '').toLowerCase();
        const digitalKeywords = ['download', 'digital', 'ebook', 'software', 'license', 'subscription', 'service', 'consultation', 'course', 'training'];

        if (digitalKeywords.some(keyword => productName.includes(keyword))) {
            return false; // Digital service/product
        }

        return true; // Default to goods if shipping is required
    }

    // CRITICAL FIX: Detect Shopify price structure (net vs gross)
    detectShopifyPriceStructure(order) {
        const subtotal = this.parseAmount(order.Subtotal || order['Line Item Subtotal'] || 0);
        const taxes = this.parseAmount(order.Taxes || order['Tax Amount'] || order['Total Tax'] || 0);
        const shipping = this.parseAmount(order.Shipping || order['Shipping Amount'] || 0);
        const total = this.parseAmount(order.Total || order['Total Amount'] || 0);

        // Method 1: Check if Subtotal + Taxes + Shipping = Total
        const calculatedTotal = subtotal + taxes + shipping;
        const totalDifference = Math.abs(calculatedTotal - total);
        const tolerance = 0.02; // 2 cent tolerance for rounding

        if (totalDifference <= tolerance) {
            // Subtotal appears to be NET (excluding VAT)
            console.log(`📊 Detected NET pricing: Subtotal (${subtotal}) + Taxes (${taxes}) + Shipping (${shipping}) = Total (${total})`);
            return {
                type: 'NET',
                confidence: 'HIGH',
                reason: 'Subtotal + Taxes + Shipping = Total',
                subtotalIsNet: true,
                subtotalIsGross: false
            };
        }

        // Method 2: Check if Subtotal + Shipping = Total (taxes included in subtotal)
        const calculatedTotalWithoutSeparateTax = subtotal + shipping;
        const totalDifferenceWithoutTax = Math.abs(calculatedTotalWithoutSeparateTax - total);

        if (totalDifferenceWithoutTax <= tolerance && taxes === 0) {
            // Subtotal appears to be GROSS (including VAT)
            console.log(`📊 Detected GROSS pricing: Subtotal (${subtotal}) + Shipping (${shipping}) = Total (${total}), no separate tax`);
            return {
                type: 'GROSS',
                confidence: 'HIGH',
                reason: 'Subtotal + Shipping = Total, no separate tax field',
                subtotalIsNet: false,
                subtotalIsGross: true
            };
        }

        // Method 3: Analyze VAT rate consistency
        if (subtotal > 0 && taxes > 0) {
            const impliedVATRate = (taxes / subtotal) * 100;
            const country = this.extractCountry(order);
            const expectedRates = this.getCountryVATRates(country);

            if (expectedRates && this.isReasonableVATRate(impliedVATRate, expectedRates)) {
                console.log(`📊 Detected NET pricing: VAT rate ${impliedVATRate.toFixed(1)}% is reasonable for ${country}`);
                return {
                    type: 'NET',
                    confidence: 'MEDIUM',
                    reason: `Implied VAT rate ${impliedVATRate.toFixed(1)}% matches country expectations`,
                    subtotalIsNet: true,
                    subtotalIsGross: false,
                    impliedVATRate: impliedVATRate
                };
            }
        }

        // Method 4: Check for tax-inclusive indicators in field names or values
        const taxInclusiveIndicators = this.checkTaxInclusiveIndicators(order);
        if (taxInclusiveIndicators.found) {
            console.log(`📊 Detected GROSS pricing: Found tax-inclusive indicators - ${taxInclusiveIndicators.reason}`);
            return {
                type: 'GROSS',
                confidence: 'MEDIUM',
                reason: taxInclusiveIndicators.reason,
                subtotalIsNet: false,
                subtotalIsGross: true
            };
        }

        // Method 5: Default assumption based on common Shopify configurations
        if (taxes > 0) {
            // If there's a separate tax field, subtotal is likely NET
            console.warn(`⚠️ ASSUMPTION: Subtotal appears to be NET (separate tax field exists)`);
            return {
                type: 'NET',
                confidence: 'LOW',
                reason: 'Assumption: separate tax field suggests net pricing',
                subtotalIsNet: true,
                subtotalIsGross: false,
                isAssumption: true
            };
        } else {
            // If no separate tax field, subtotal might be GROSS
            console.warn(`⚠️ ASSUMPTION: Subtotal might be GROSS (no separate tax field)`);
            return {
                type: 'GROSS',
                confidence: 'LOW',
                reason: 'Assumption: no separate tax field suggests gross pricing',
                subtotalIsNet: false,
                subtotalIsGross: true,
                isAssumption: true
            };
        }
    }

    extractVATRate(order) {
        // Try to extract VAT rate from tax name or rate columns
        const taxName = order['Tax name'] || order['Tax Name'] || order['Tax 1 Name'] || '';
        const taxRate = order['Tax Rate'] || order['Tax 1 Rate'] || order['Tax 1 Value'] || '';

        // Look for percentage in tax name (e.g., "VAT 19%", "IVA 22%")
        let match = taxName.match(/(\d+(?:\.\d+)?)%/);
        if (match) {
            const rate = parseFloat(match[1]);
            if (rate >= 0 && rate <= 50) return rate; // Reasonable VAT rate range
        }

        // Look for rate in tax rate column
        if (taxRate) {
            const cleaned = taxRate.toString().replace(/[^\d.]/g, '');
            const rate = parseFloat(cleaned);
            if (rate > 0 && rate <= 50) return rate; // Reasonable VAT rate range
        }

        // Try to calculate from tax amount and subtotal
        const taxAmount = this.parseAmount(order['Tax Amount'] || order['Taxes'] || 0);
        const subtotal = this.parseAmount(order['Subtotal'] || 0);

        if (taxAmount > 0 && subtotal > 0) {
            const calculatedRate = (taxAmount / subtotal) * 100;
            if (calculatedRate >= 0 && calculatedRate <= 50) {
                return Math.round(calculatedRate * 10) / 10; // Round to 1 decimal place
            }
        }

        // No valid rate found - will be determined by product/country logic
        return 0;
    }

    // Check for tax-inclusive indicators in order data
    checkTaxInclusiveIndicators(order) {
        // Check field names for tax-inclusive indicators
        const fieldNames = Object.keys(order).join(' ').toLowerCase();

        if (fieldNames.includes('tax inclusive') || fieldNames.includes('tax_inclusive') ||
            fieldNames.includes('prices include tax') || fieldNames.includes('incl tax')) {
            return {
                found: true,
                reason: 'Field names indicate tax-inclusive pricing'
            };
        }

        // Check for Shopify-specific tax settings
        if (order['Tax Included'] === 'true' || order['Tax Included'] === true ||
            order['Prices Include Tax'] === 'true' || order['Prices Include Tax'] === true) {
            return {
                found: true,
                reason: 'Shopify tax settings indicate inclusive pricing'
            };
        }

        // Check tax name for inclusive indicators
        const taxName = (order['Tax name'] || order['Tax Name'] || '').toLowerCase();
        if (taxName.includes('inclusive') || taxName.includes('incl') || taxName.includes('included')) {
            return {
                found: true,
                reason: 'Tax name indicates inclusive pricing'
            };
        }

        return { found: false };
    }

    // Check if VAT rate is reasonable for the country
    isReasonableVATRate(rate, countryRates) {
        if (!countryRates) return false;

        const allValidRates = [
            countryRates.standard,
            ...(countryRates.reduced || []),
            ...(countryRates.superReduced || []),
            countryRates.parking || 0,
            0 // Zero rate
        ].filter(r => r !== undefined);

        // Allow 1% tolerance for rate matching
        return allValidRates.some(validRate => Math.abs(rate - validRate) <= 1.0);
    }

    // CRITICAL FIX: Extract financial data with proper net/gross handling
    extractFinancialData(order, priceStructure) {
        const rawSubtotal = this.parseAmount(order.Subtotal || order['Line Item Subtotal'] || 0);
        const rawTaxes = this.parseAmount(order.Taxes || order['Tax Amount'] || order['Total Tax'] || 0);
        const rawShipping = this.parseAmount(order.Shipping || order['Shipping Amount'] || 0);
        const rawTotal = this.parseAmount(order.Total || order['Total Amount'] || 0);

        // CRITICAL FIX: Handle Shopify discount amounts properly
        const discountInfo = this.extractDiscountInfo(order);
        console.log(`💸 Discount info:`, discountInfo);

        // CRITICAL FIX: Handle partial refunds from Shopify orders
        const refundInfo = this.extractRefundInfo(order);
        console.log(`🔄 Refund info:`, refundInfo);

        let netValue, grossValue, vatAmount, subtotal, taxes, shipping, total;

        if (priceStructure.subtotalIsNet) {
            // SCENARIO 1: Subtotal is NET (excluding VAT) - ALREADY POST-DISCOUNT
            netValue = rawSubtotal;  // This is already discounted net value
            vatAmount = rawTaxes;
            grossValue = rawSubtotal + rawTaxes;

            subtotal = rawSubtotal; // Net value (post-discount)
            taxes = rawTaxes;       // VAT amount
            shipping = rawShipping; // Shipping (may include VAT)
            total = rawTotal;       // Total including VAT

            console.log(`💰 NET pricing (post-discount): Net=${netValue}, VAT=${vatAmount}, Gross=${grossValue}`);
            if (discountInfo.hasDiscount) {
                console.log(`💸 Discount of ${discountInfo.discountAmount} already deducted from subtotal`);
            }

        } else if (priceStructure.subtotalIsGross) {
            // SCENARIO 2: Subtotal is GROSS (including VAT) - ALREADY POST-DISCOUNT
            grossValue = rawSubtotal; // This is already discounted gross value

            // Calculate net value by removing VAT
            const country = this.extractCountry(order);
            const postalCode = this.extractPostalCode(order);
            const productType = order['Lineitem name'] || order['Product Name'] || '';
            const productCategory = order['Product Category'] || order['Category'] || '';

            // Get applicable VAT rate
            const vatRate = priceStructure.impliedVATRate ||
                           this.getApplicableVATRate(country, postalCode, productType, null, productCategory);

            if (vatRate > 0) {
                netValue = grossValue / (1 + vatRate / 100);
                vatAmount = grossValue - netValue;
            } else {
                // No VAT rate available, assume gross = net
                netValue = grossValue;
                vatAmount = 0;
            }

            subtotal = netValue;    // Calculated net value (post-discount)
            taxes = vatAmount;      // Calculated VAT amount
            shipping = rawShipping; // Shipping
            total = rawTotal;       // Total

            console.log(`💰 GROSS pricing (post-discount): Gross=${grossValue}, Net=${netValue}, VAT=${vatAmount}, Rate=${vatRate}%`);
            if (discountInfo.hasDiscount) {
                console.log(`💸 Discount of ${discountInfo.discountAmount} already deducted from subtotal`);
            }

        } else {
            // FALLBACK: Use raw values as-is
            console.warn(`⚠️ Unknown pricing structure, using raw values`);
            subtotal = rawSubtotal;
            taxes = rawTaxes;
            shipping = rawShipping;
            total = rawTotal;
            netValue = rawSubtotal;
            grossValue = rawSubtotal + rawTaxes;
            vatAmount = rawTaxes;
        }

        // CRITICAL: Validate that we're not double-deducting discounts
        if (discountInfo.hasDiscount && !discountInfo.validation.isConsistent) {
            console.warn(`⚠️ Discount validation issues detected:`, discountInfo.validation.issues);
        }

        // CRITICAL FIX: Apply refund adjustments to get final IOSS amounts
        const baseFinancialData = {
            subtotal,
            taxes,
            shipping,
            total,
            netValue,
            grossValue,
            vatAmount,
            priceStructure,
            discountInfo,
            refundInfo,
            rawValues: {
                subtotal: rawSubtotal,
                taxes: rawTaxes,
                shipping: rawShipping,
                total: rawTotal
            }
        };

        // Apply refund adjustments to get final reportable amounts
        const adjustedFinancialData = this.applyRefundAdjustments(baseFinancialData, refundInfo);

        if (adjustedFinancialData.isAdjustedForRefunds) {
            console.log(`🔄 Applied refund adjustment:`, adjustedFinancialData.refundAdjustment);
        }

        return adjustedFinancialData;
    }

    // CRITICAL DEBUG: Analyze why orders are being excluded from IOSS
    analyzeIOSSEligibility(allOrders) {
        const analysis = {
            totalOrders: allOrders.length,
            eligible: 0,
            excludedReasons: {
                'Non-EU destination': 0,
                'Over €150 threshold': 0,
                'B2B transaction (customer has VAT number)': 0,
                'Service/Digital product': 0,
                'Invalid or zero VAT rate': 0,
                'Order fully refunded': 0,
                'No goods items (services only)': 0,
                'Other': 0
            },
            sampleExclusions: []
        };

        allOrders.forEach((order, index) => {
            if (order.isIOSSEligible) {
                analysis.eligible++;
            } else {
                const reason = order.exclusionReason || 'Other';
                if (analysis.excludedReasons.hasOwnProperty(reason)) {
                    analysis.excludedReasons[reason]++;
                } else {
                    analysis.excludedReasons['Other']++;
                }

                // Collect sample exclusions for debugging
                if (analysis.sampleExclusions.length < 10) {
                    analysis.sampleExclusions.push({
                        orderId: order.orderId,
                        country: order.country,
                        total: order.total,
                        vatRate: order.vatRate,
                        reason: reason,
                        customerVATNumber: order.customerVATNumber,
                        requiresShipping: order.requiresShipping,
                        isEUDestination: order.isEUDestination,
                        isUnderThreshold: order.isUnderThreshold,
                        isB2CTransaction: order.isB2CTransaction,
                        isGoodsSupply: order.isGoodsSupply
                    });
                }
            }
        });

        // Calculate percentages
        analysis.eligiblePercentage = (analysis.eligible / analysis.totalOrders * 100).toFixed(1);

        // Log detailed breakdown
        console.log(`📊 IOSS Eligibility Breakdown:`);
        console.log(`   Total Orders: ${analysis.totalOrders}`);
        console.log(`   IOSS Eligible: ${analysis.eligible} (${analysis.eligiblePercentage}%)`);
        console.log(`   Excluded: ${analysis.totalOrders - analysis.eligible}`);
        console.log(`   Exclusion Reasons:`);

        Object.entries(analysis.excludedReasons).forEach(([reason, count]) => {
            if (count > 0) {
                const percentage = (count / analysis.totalOrders * 100).toFixed(1);
                console.log(`     - ${reason}: ${count} (${percentage}%)`);
            }
        });

        // Log sample exclusions
        if (analysis.sampleExclusions.length > 0) {
            console.log(`   Sample Excluded Orders:`);
            analysis.sampleExclusions.forEach((sample, index) => {
                console.log(`     ${index + 1}. ${sample.orderId} (${sample.country}): ${sample.reason}`);
                console.log(`        Total: €${sample.total}, VAT: ${sample.vatRate}%, EU: ${sample.isEUDestination}, B2C: ${sample.isB2CTransaction}, Goods: ${sample.isGoodsSupply}`);
            });
        }

        return analysis;
    }

    // SPECIFIC DEBUG: Analyze XI (Northern Ireland) orders
    analyzeXIOrders(allOrders) {
        const xiOrders = allOrders.filter(order => order.country === 'XI');

        const analysis = {
            totalXIOrders: xiOrders.length,
            eligibleXI: xiOrders.filter(order => order.isIOSSEligible).length,
            excludedXI: xiOrders.filter(order => !order.isIOSSEligible).length,
            xiExclusionReasons: {},
            sampleXIOrders: []
        };

        // Analyze XI exclusion reasons
        xiOrders.forEach(order => {
            if (!order.isIOSSEligible) {
                const reason = order.exclusionReason || 'Unknown';
                analysis.xiExclusionReasons[reason] = (analysis.xiExclusionReasons[reason] || 0) + 1;
            }

            // Collect sample XI orders
            if (analysis.sampleXIOrders.length < 5) {
                analysis.sampleXIOrders.push({
                    orderId: order.orderId,
                    isEligible: order.isIOSSEligible,
                    exclusionReason: order.exclusionReason,
                    netValue: order.netValue || order.subtotal,
                    vatRate: order.vatRate,
                    isGoodsSupply: order.isGoodsSupply,
                    isB2CTransaction: order.isB2CTransaction,
                    requiresShipping: order.requiresShipping
                });
            }
        });

        // Log detailed XI analysis
        console.log(`🇬🇧 XI Orders Breakdown:`);
        console.log(`   Total XI Orders: ${analysis.totalXIOrders}`);
        console.log(`   XI IOSS Eligible: ${analysis.eligibleXI}`);
        console.log(`   XI Excluded: ${analysis.excludedXI}`);

        if (analysis.excludedXI > 0) {
            console.log(`   XI Exclusion Reasons:`);
            Object.entries(analysis.xiExclusionReasons).forEach(([reason, count]) => {
                console.log(`     - ${reason}: ${count}`);
            });
        }

        if (analysis.sampleXIOrders.length > 0) {
            console.log(`   Sample XI Orders:`);
            analysis.sampleXIOrders.forEach((sample, index) => {
                console.log(`     ${index + 1}. ${sample.orderId}: ${sample.isEligible ? '✅ ELIGIBLE' : '❌ EXCLUDED'}`);
                if (!sample.isEligible) {
                    console.log(`        Reason: ${sample.exclusionReason}`);
                    console.log(`        Details: Net=€${sample.netValue}, VAT=${sample.vatRate}%, Goods=${sample.isGoodsSupply}, B2C=${sample.isB2CTransaction}`);
                }
            });
        }

        return analysis;
    }

    // Get country name for display
    getCountryName(countryCode) {
        const countryNames = {
            'AT': 'Austria', 'BE': 'Belgium', 'BG': 'Bulgaria', 'HR': 'Croatia',
            'CY': 'Cyprus', 'CZ': 'Czech Republic', 'DK': 'Denmark', 'EE': 'Estonia',
            'FI': 'Finland', 'FR': 'France', 'DE': 'Germany', 'GR': 'Greece',
            'EL': 'Greece', 'HU': 'Hungary', 'IE': 'Ireland', 'IT': 'Italy',
            'LV': 'Latvia', 'LT': 'Lithuania', 'LU': 'Luxembourg', 'MT': 'Malta',
            'NL': 'Netherlands', 'PL': 'Poland', 'PT': 'Portugal', 'RO': 'Romania',
            'SK': 'Slovakia', 'SI': 'Slovenia', 'ES': 'Spain', 'SE': 'Sweden',
            'XI': 'Northern Ireland', // ADDED: Northern Ireland
            'US': 'United States', 'GB': 'Great Britain', 'CA': 'Canada'
        };

        return countryNames[countryCode] || countryCode;
    }

    // CRITICAL FIX: Extract and validate Shopify discount information
    extractDiscountInfo(order) {
        const discountCode = order['Discount Code'] || order['Discount code'] || '';
        const discountAmount = this.parseAmount(order['Discount Amount'] || order['Discount amount'] || 0);
        const lineitemDiscount = this.parseAmount(order['Lineitem discount'] || 0);

        // Extract original prices before discount
        const lineitemPrice = this.parseAmount(order['Lineitem price'] || 0);
        const lineitemComparePrice = this.parseAmount(order['Lineitem compare at price'] || 0);
        const lineitemQuantity = this.parseAmount(order['Lineitem quantity'] || 1);

        // Calculate discount validation
        const validation = this.validateDiscountConsistency(order, discountAmount, lineitemDiscount);

        return {
            discountCode,
            discountAmount,
            lineitemDiscount,
            lineitemPrice,
            lineitemComparePrice,
            lineitemQuantity,
            hasDiscount: discountAmount > 0 || lineitemDiscount > 0 || discountCode.length > 0,
            validation,
            // CRITICAL: Shopify subtotal is ALREADY discounted
            isAlreadyDeducted: true,
            warning: validation.hasIssues ? validation.issues : null
        };
    }

    // Validate discount consistency in Shopify data
    validateDiscountConsistency(order, discountAmount, lineitemDiscount) {
        const issues = [];
        let hasIssues = false;

        const subtotal = this.parseAmount(order.Subtotal || 0);
        const lineitemPrice = this.parseAmount(order['Lineitem price'] || 0);
        const lineitemQuantity = this.parseAmount(order['Lineitem quantity'] || 1);
        const lineitemComparePrice = this.parseAmount(order['Lineitem compare at price'] || 0);

        // Check 1: Discount amount vs line item discount consistency
        if (discountAmount > 0 && lineitemDiscount > 0) {
            const tolerance = 0.02; // 2 cent tolerance
            if (Math.abs(discountAmount - lineitemDiscount) > tolerance) {
                issues.push(`Discount amount (${discountAmount}) doesn't match line item discount (${lineitemDiscount})`);
                hasIssues = true;
            }
        }

        // Check 2: Verify Shopify subtotal is post-discount
        if (lineitemPrice > 0 && lineitemQuantity > 0) {
            const expectedSubtotalBeforeDiscount = lineitemPrice * lineitemQuantity;
            const expectedSubtotalAfterDiscount = expectedSubtotalBeforeDiscount - (lineitemDiscount || discountAmount);

            const tolerance = 0.02;
            if (Math.abs(subtotal - expectedSubtotalAfterDiscount) > tolerance) {
                // This might indicate subtotal is NOT post-discount
                issues.push(`Subtotal (${subtotal}) doesn't match expected post-discount amount (${expectedSubtotalAfterDiscount})`);
                hasIssues = true;
            } else {
                console.log(`✅ Confirmed: Shopify subtotal (${subtotal}) is post-discount`);
            }
        }

        // Check 3: Compare at price validation
        if (lineitemComparePrice > 0 && lineitemPrice > 0) {
            if (lineitemComparePrice <= lineitemPrice) {
                issues.push(`Compare at price (${lineitemComparePrice}) should be higher than sale price (${lineitemPrice})`);
                hasIssues = true;
            }
        }

        // Check 4: Unreasonable discount amounts
        if (discountAmount > subtotal * 2) {
            issues.push(`Discount amount (${discountAmount}) seems unreasonably high compared to subtotal (${subtotal})`);
            hasIssues = true;
        }

        return {
            hasIssues,
            issues,
            isConsistent: !hasIssues,
            subtotalIsPostDiscount: !hasIssues // If no issues, assume subtotal is post-discount
        };
    }

    // CRITICAL: Ensure IOSS taxable amount is calculated correctly with discounts
    calculateIOSSTaxableAmount(financialData, vatRate) {
        const { netValue, discountInfo, priceStructure } = financialData;

        // IMPORTANT: Shopify subtotal is already post-discount
        // We should NOT deduct discount again

        if (!discountInfo.hasDiscount) {
            // No discount, use calculated net value
            return {
                taxableAmount: netValue,
                discountApplied: 0,
                note: 'No discount applied'
            };
        }

        if (discountInfo.validation.subtotalIsPostDiscount) {
            // Shopify subtotal is already discounted - use as-is
            return {
                taxableAmount: netValue,
                discountApplied: discountInfo.discountAmount,
                note: 'Discount already deducted in Shopify subtotal',
                originalAmount: netValue + discountInfo.discountAmount
            };
        } else {
            // Unusual case: might need manual adjustment
            console.warn(`⚠️ Discount consistency issues detected for order`);
            return {
                taxableAmount: netValue,
                discountApplied: discountInfo.discountAmount,
                note: 'Discount validation failed - manual review recommended',
                hasValidationIssues: true,
                validationIssues: discountInfo.validation.issues
            };
        }
    }

    getDefaultVATRate(countryCode) {
        const defaultRates = {
            'AT': 20, 'BE': 21, 'BG': 20, 'HR': 25, 'CY': 19, 'CZ': 21,
            'DK': 25, 'EE': 22, 'FI': 25.5, 'FR': 20, 'DE': 19, 'GR': 24,
            'EL': 24, 'HU': 27, 'IE': 23, 'IT': 22, 'LV': 21, 'LT': 21,
            'LU': 17, 'MT': 18, 'NL': 21, 'PL': 23, 'PT': 23, 'RO': 19,
            'SK': 23, 'SI': 22, 'ES': 21, 'SE': 25, 'XI': 20  // Added XI for Northern Ireland
        };
        return defaultRates[countryCode] || 0;
    }

    // CRITICAL FIX: B2C Transaction Verification
    isB2CTransaction(order) {
        // IOSS only applies to B2C transactions (no customer VAT number)
        const vatNumber = (order['Customer VAT Number'] || order['VAT Number'] || order['Tax Number'] || '').trim();

        if (vatNumber && vatNumber.length > 0) {
            // Check if it's a valid VAT number format
            if (this.isValidVATNumberFormat(vatNumber)) {
                return false; // B2B transaction
            }
        }

        return true; // No VAT number = B2C transaction
    }

    // VAT Number Format Validation
    isValidVATNumberFormat(vatNumber) {
        // Basic VAT number format validation for EU countries
        const vatPatterns = {
            'AT': /^ATU\d{8}$/,
            'BE': /^BE[01]\d{9}$/,
            'BG': /^BG\d{9,10}$/,
            'HR': /^HR\d{11}$/,
            'CY': /^CY\d{8}[A-Z]$/,
            'CZ': /^CZ\d{8,10}$/,
            'DK': /^DK\d{8}$/,
            'EE': /^EE\d{9}$/,
            'FI': /^FI\d{8}$/,
            'FR': /^FR[A-Z0-9]{2}\d{9}$/,
            'DE': /^DE\d{9}$/,
            'GR': /^(EL|GR)\d{9}$/,
            'HU': /^HU\d{8}$/,
            'IE': /^IE\d[A-Z0-9]\d{5}[A-Z]$/,
            'IT': /^IT\d{11}$/,
            'LV': /^LV\d{11}$/,
            'LT': /^LT(\d{9}|\d{12})$/,
            'LU': /^LU\d{8}$/,
            'MT': /^MT\d{8}$/,
            'NL': /^NL\d{9}B\d{2}$/,
            'PL': /^PL\d{10}$/,
            'PT': /^PT\d{9}$/,
            'RO': /^RO\d{2,10}$/,
            'SK': /^SK\d{10}$/,
            'SI': /^SI\d{8}$/,
            'ES': /^ES[A-Z0-9]\d{7}[A-Z0-9]$/,
            'SE': /^SE\d{12}$/
        };

        // Extract country code from VAT number
        const countryCode = vatNumber.substring(0, 2).toUpperCase();
        const pattern = vatPatterns[countryCode];

        return pattern ? pattern.test(vatNumber.toUpperCase()) : false;
    }

    // CRITICAL FIX: Supply Type Classification
    isGoodsSupply(order) {
        // Check if this is a goods supply (physical products requiring shipping)
        const requiresShipping = (order['Lineitem requires shipping'] || 'true').toLowerCase();

        if (requiresShipping === 'false' || requiresShipping === 'no') {
            // Likely a service or digital product
            return false;
        }

        // Check product type for digital services
        const productName = (order['Lineitem name'] || '').toLowerCase();
        const digitalKeywords = ['download', 'digital', 'ebook', 'software', 'license', 'subscription', 'service', 'consultation', 'course', 'training'];

        if (digitalKeywords.some(keyword => productName.includes(keyword))) {
            return false; // Digital service/product
        }

        return true; // Default to goods if shipping is required
    }

    calculateSummary() {
        const allOrders = this.processedData.orders;
        const eligibleOrders = allOrders.filter(o => o.isIOSSEligible);

        // CRITICAL DEBUG: Analyze IOSS eligibility exclusions
        const eligibilityAnalysis = this.analyzeIOSSEligibility(allOrders);
        console.log('🔍 IOSS Eligibility Analysis:', eligibilityAnalysis);

        // SPECIFIC DEBUG: Analyze XI orders
        const xiAnalysis = this.analyzeXIOrders(allOrders);
        console.log('🇬🇧 XI (Northern Ireland) Analysis:', xiAnalysis);

        // Calculate discount statistics
        const ordersWithDiscounts = allOrders.filter(o => o.discountInfo?.hasDiscount);
        const totalDiscountAmount = ordersWithDiscounts.reduce((sum, o) => sum + (o.discountInfo?.discountAmount || 0), 0);

        // Calculate refund statistics
        const ordersWithRefunds = allOrders.filter(o => o.refundInfo?.hasPartialRefund);
        const fullyRefundedOrders = allOrders.filter(o => o.refundInfo?.isFullyRefunded);
        const partiallyRefundedOrders = allOrders.filter(o => o.refundInfo?.isPartiallyRefunded);
        const totalRefundAmount = ordersWithRefunds.reduce((sum, o) => sum + (o.refundInfo?.refundedAmount || 0), 0);
        const adjustedOrders = allOrders.filter(o => o.isAdjustedForRefunds);

        // CRITICAL FIX: Calculate proper VAT amounts
        const { calculatedVAT, shopifyVAT } = this.calculateVATTotals(eligibleOrders);

        this.processedData.summary = {
            totalOrders: allOrders.length,
            iossEligibleOrders: eligibleOrders.length,
            excludedOrders: allOrders.length - eligibleOrders.length,
            totalVatCalculated: calculatedVAT,     // FIXED: Properly calculated VAT
            totalVatShopify: shopifyVAT,           // FIXED: Shopify reported VAT
            totalValueEUR: eligibleOrders.reduce((sum, o) => sum + o.total, 0),
            excludedValue: allOrders.filter(o => !o.isIOSSEligible).reduce((sum, o) => sum + o.total, 0),
            countryCount: new Set(eligibleOrders.map(o => o.country)).size,
            // ADDED: Discount statistics
            ordersWithDiscounts: ordersWithDiscounts.length,
            totalDiscountAmount: totalDiscountAmount,
            averageDiscountAmount: ordersWithDiscounts.length > 0 ? totalDiscountAmount / ordersWithDiscounts.length : 0,
            // ADDED: Refund statistics
            ordersWithRefunds: ordersWithRefunds.length,
            fullyRefundedOrders: fullyRefundedOrders.length,
            partiallyRefundedOrders: partiallyRefundedOrders.length,
            totalRefundAmount: totalRefundAmount,
            adjustedOrders: adjustedOrders.length,
            averageRefundAmount: ordersWithRefunds.length > 0 ? totalRefundAmount / ordersWithRefunds.length : 0
        };

        // Create breakdown by country and VAT rate
        this.processedData.breakdown = {};

        // DEBUGGING: Log all eligible orders
        console.log(`🔍 Creating breakdown for ${eligibleOrders.length} eligible orders:`);
        eligibleOrders.forEach((order, index) => {
            if (index < 5) { // Log first 5 orders
                console.log(`   Order ${index + 1}: ${order.country} @ ${order.vatRate}% (${order.orderId})`);
            }
        });

        eligibleOrders.forEach(order => {
            const key = `${order.country}-${order.vatRate}`;
            if (!this.processedData.breakdown[key]) {
                this.processedData.breakdown[key] = {
                    country: order.country,
                    vatRate: order.vatRate,
                    orders: 0,
                    netValue: 0,
                    vatAmount: 0,
                    shopifyNetValue: 0,
                    shopifyVatAmount: 0
                };
                console.log(`📊 Created breakdown entry: ${key}`);
            }

            const breakdown = this.processedData.breakdown[key];
            breakdown.orders++;

            // FIXED: Use proper taxable amount and calculated VAT
            const taxableAmount = this.calculateTaxableAmount(order);
            const calculatedVAT = order.calculatedVAT || this.calculateProperVAT(order);

            breakdown.netValue += taxableAmount;
            breakdown.vatAmount += calculatedVAT;

            // Keep Shopify values as they were reported
            breakdown.shopifyNetValue += order.subtotal + order.shipping;
            breakdown.shopifyVatAmount += order.taxes;
        });

        console.log('📊 Summary calculated:', this.processedData.summary);

        // DEBUGGING: Log final breakdown
        console.log('🔍 Final breakdown entries:');
        Object.entries(this.processedData.breakdown).forEach(([key, data]) => {
            console.log(`   ${key}: ${data.orders} orders, €${data.vatAmount.toFixed(2)} VAT`);
        });
    }

    // CRITICAL FIX: Calculate proper VAT totals (Calculated vs Shopify)
    calculateVATTotals(eligibleOrders) {
        let calculatedVAT = 0;
        let shopifyVAT = 0;

        eligibleOrders.forEach(order => {
            // Shopify VAT = what Shopify reported in the taxes field
            shopifyVAT += order.taxes;

            // Calculated VAT = properly calculated based on net amounts and VAT rates
            const properVAT = this.calculateProperVAT(order);
            calculatedVAT += properVAT;

            // Store both values in the order for detailed breakdown
            order.calculatedVAT = properVAT;
            order.shopifyVAT = order.taxes;
            order.vatDifference = properVAT - order.taxes;
        });

        console.log(`💰 VAT Totals: Calculated=€${calculatedVAT.toFixed(2)}, Shopify=€${shopifyVAT.toFixed(2)}, Difference=€${(calculatedVAT - shopifyVAT).toFixed(2)}`);

        // DEBUGGING: Log detailed breakdown
        console.log(`🔍 VAT Calculation Details:`);
        console.log(`   - Orders processed: ${eligibleOrders.length}`);
        console.log(`   - Average calculated VAT per order: €${(calculatedVAT / eligibleOrders.length).toFixed(2)}`);
        console.log(`   - Average Shopify VAT per order: €${(shopifyVAT / eligibleOrders.length).toFixed(2)}`);

        // Log first few orders for debugging
        eligibleOrders.slice(0, 3).forEach((order, index) => {
            console.log(`   Order ${index + 1} (${order.orderId}): Calc=€${order.calculatedVAT?.toFixed(2)}, Shopify=€${order.shopifyVAT?.toFixed(2)}, Rate=${order.vatRate}%`);
        });

        return { calculatedVAT, shopifyVAT };
    }

    // Calculate proper VAT for an order based on IOSS rules
    calculateProperVAT(order) {
        // CRITICAL FIX: Calculate proper taxable amount for VAT calculation
        const taxableAmount = this.calculateTaxableAmount(order);
        const vatRate = order.vatRate;

        if (!taxableAmount || !vatRate) {
            console.warn(`⚠️ Missing data for VAT calculation: taxableAmount=${taxableAmount}, vatRate=${vatRate}%`);
            return 0;
        }

        // Calculate VAT: Taxable Amount × (VAT Rate / 100)
        const calculatedVAT = taxableAmount * (vatRate / 100);

        console.log(`🧮 VAT Calc: €${taxableAmount.toFixed(2)} × ${vatRate}% = €${calculatedVAT.toFixed(2)} (Order: ${order.orderId})`);

        return calculatedVAT;
    }

    // CRITICAL FIX: Calculate the correct taxable amount for IOSS VAT calculation
    calculateTaxableAmount(order) {
        // The taxable amount should be the total value excluding VAT
        // This depends on whether Shopify prices are NET or GROSS

        const priceStructure = order.priceStructure;
        const total = order.total;
        const subtotal = order.subtotal;
        const shipping = order.shipping || 0;
        const vatRate = order.vatRate;

        if (!priceStructure) {
            console.warn(`⚠️ No price structure info for order ${order.orderId}`);
            return subtotal + shipping; // Fallback
        }

        if (priceStructure.subtotalIsNet) {
            // NET pricing: Subtotal + Shipping = Taxable Amount
            const taxableAmount = subtotal + shipping;
            console.log(`📊 NET pricing: Subtotal(${subtotal}) + Shipping(${shipping}) = Taxable(${taxableAmount})`);
            return taxableAmount;

        } else if (priceStructure.subtotalIsGross) {
            // GROSS pricing: Need to extract net amount from gross total
            if (vatRate > 0) {
                const taxableAmount = total / (1 + vatRate / 100);
                console.log(`📊 GROSS pricing: Total(${total}) ÷ (1 + ${vatRate}%) = Taxable(${taxableAmount.toFixed(2)})`);
                return taxableAmount;
            } else {
                console.warn(`⚠️ GROSS pricing but no VAT rate for order ${order.orderId}`);
                return total; // Fallback
            }
        } else {
            // Unknown pricing structure - use conservative approach
            console.warn(`⚠️ Unknown pricing structure for order ${order.orderId}`);

            // Try to calculate from total and VAT rate
            if (vatRate > 0 && total > 0) {
                // Assume total includes VAT, calculate backwards
                const taxableAmount = total / (1 + vatRate / 100);
                console.log(`📊 Fallback calc: Total(${total}) ÷ (1 + ${vatRate}%) = Taxable(${taxableAmount.toFixed(2)})`);
                return taxableAmount;
            }

            // Last resort: use subtotal + shipping
            return subtotal + shipping;
        }
    }

    displayResults() {
        const currencySymbol = this.getCurrencySymbol(this.companyInfo.currency);
        const summary = this.processedData.summary;

        // Update summary statistics
        this.updateElement('total-orders', summary.totalOrders);
        this.updateElement('ioss-orders', summary.iossEligibleOrders);
        this.updateElement('total-vat-calculated', `${currencySymbol}${summary.totalVatCalculated.toFixed(2)}`);
        this.updateElement('total-vat-shopify', `${currencySymbol}${summary.totalVatShopify.toFixed(2)}`);
        this.updateElement('countries-count', summary.countryCount);

        // ADDED: Show VAT difference analysis
        const vatDifference = summary.totalVatCalculated - summary.totalVatShopify;
        const vatDifferencePercent = summary.totalVatShopify > 0 ? (vatDifference / summary.totalVatShopify) * 100 : 0;

        // Update VAT difference display
        const vatDiffElement = document.getElementById('vat-difference');
        if (vatDiffElement) {
            const diffClass = vatDifference > 0 ? 'positive' : vatDifference < 0 ? 'negative' : 'neutral';
            vatDiffElement.innerHTML = `
                <span class="vat-diff ${diffClass}">
                    ${vatDifference >= 0 ? '+' : ''}${currencySymbol}${vatDifference.toFixed(2)}
                    (${vatDifferencePercent >= 0 ? '+' : ''}${vatDifferencePercent.toFixed(1)}%)
                </span>
            `;
        }

        // Update breakdown table
        const tableBody = document.getElementById('country-breakdown');
        if (tableBody) {
            tableBody.innerHTML = '';

            Object.values(this.processedData.breakdown).forEach(data => {
                const row = document.createElement('tr');
                const countryName = this.getCountryName(data.country);
                row.innerHTML = `
                    <td>${countryName} (${data.country})</td>
                    <td>${data.vatRate.toFixed(1)}%</td>
                    <td>${data.orders}</td>
                    <td>${currencySymbol}${data.netValue.toFixed(2)}</td>
                    <td>${currencySymbol}${data.vatAmount.toFixed(2)}</td>
                    <td>${currencySymbol}${data.shopifyNetValue.toFixed(2)}</td>
                    <td>${currencySymbol}${data.shopifyVatAmount.toFixed(2)}</td>
                `;
                tableBody.appendChild(row);
            });
        }

        // Show issues
        this.displayIssues();

        // Show results section
        this.showElement('results-section');
        this.showElement('export-buttons');
        this.hideProcessingStatus();
        
        console.log('✅ Results displayed');
    }

    processRefunds() {
        this.showRefundsProcessingStatus('Processing refunds...');

        try {
            // ENHANCED: Comprehensive refunds processing
            const refunds = this.identifyRefunds(this.refundsData);
            const processedRefunds = this.processRefundsForIOSS(refunds);
            const refundsByPeriod = this.groupRefundsByOriginalPeriod(processedRefunds);
            const adjustmentCalculations = this.calculateRefundAdjustments(refundsByPeriod);

            // Update refunds results with enhanced data
            this.updateElement('refunds-total', refunds.length);
            this.updateElement('refunds-eligible', processedRefunds.eligible.length);
            this.updateElement('refunds-excluded', processedRefunds.excluded.length);
            this.updateElement('refunds-periods', Object.keys(refundsByPeriod).length);

            // Store comprehensive processed refunds data
            this.processedRefunds = {
                allRefunds: refunds,
                eligibleRefunds: processedRefunds.eligible,
                excludedRefunds: processedRefunds.excluded,
                refundsByPeriod: refundsByPeriod,
                adjustmentCalculations: adjustmentCalculations,
                periods: Object.keys(refundsByPeriod)
            };

            // Display detailed refunds breakdown
            this.displayRefundsBreakdown();

            this.showElement('refunds-results-section');
            this.showElement('refunds-results-content');
            this.showElement('refunds-export-buttons');
            this.hideRefundsProcessingStatus();

            console.log(`✅ Enhanced refunds processing completed: ${refunds.length} total, ${processedRefunds.eligible.length} eligible, ${Object.keys(refundsByPeriod).length} periods`);

        } catch (error) {
            this.addIssue('error', `Refunds processing failed: ${error.message}`);
            this.hideRefundsProcessingStatus();
            console.error('❌ Refunds processing failed:', error);
        }
    }

    // ENHANCED: Identify refunds with better logic
    identifyRefunds(data) {
        return data.filter(row => {
            const taxAmount = parseFloat(row['Tax amount'] || row['Tax Amount'] || 0);
            const totalAmount = parseFloat(row['Total'] || row['Total Amount'] || 0);

            // Check for negative tax amounts or negative totals
            return taxAmount < 0 || totalAmount < 0;
        });
    }

    // ENHANCED: Process refunds for IOSS eligibility with detailed checks
    processRefundsForIOSS(refunds) {
        const eligible = [];
        const excluded = [];

        refunds.forEach(refund => {
            const country = this.extractCountry(refund);
            const taxAmount = Math.abs(parseFloat(refund['Tax amount'] || refund['Tax Amount'] || 0));
            const totalAmount = Math.abs(parseFloat(refund['Total'] || refund['Total Amount'] || 0));

            // Enhanced eligibility check
            const isEUDestination = this.isEUDestination(country);
            const isSignificantAmount = taxAmount > 0.01 || totalAmount > 0.01;
            const hasValidDate = this.extractRefundDate(refund) !== null;

            const processedRefund = {
                ...refund,
                country: country,
                taxAmount: taxAmount,
                totalAmount: totalAmount,
                refundDate: this.extractRefundDate(refund),
                originalPeriod: this.estimateOriginalPeriod(refund),
                vatRate: this.extractVATRate(refund),
                isEUDestination: isEUDestination,
                isSignificantAmount: isSignificantAmount,
                hasValidDate: hasValidDate
            };

            if (isEUDestination && isSignificantAmount && hasValidDate) {
                eligible.push(processedRefund);
            } else {
                processedRefund.exclusionReason = this.getRefundExclusionReason(processedRefund);
                excluded.push(processedRefund);
            }
        });

        return { eligible, excluded };
    }

    // Extract refund date with multiple fallbacks
    extractRefundDate(refund) {
        const dateFields = ['Day', 'Date', 'Created at', 'Refund Date', 'Transaction Date'];

        for (const field of dateFields) {
            const dateValue = refund[field];
            if (dateValue) {
                const parsedDate = new Date(dateValue);
                if (!isNaN(parsedDate.getTime())) {
                    return parsedDate;
                }
            }
        }

        return null;
    }

    // Estimate original transaction period from refund
    estimateOriginalPeriod(refund) {
        const refundDate = this.extractRefundDate(refund);
        if (!refundDate) return 'Unknown';

        // For IOSS, assume original transaction was in previous months
        // This is a simplified estimation - in practice, you'd match with original orders
        const estimatedOriginal = new Date(refundDate);
        estimatedOriginal.setMonth(estimatedOriginal.getMonth() - 1);

        return `${estimatedOriginal.getFullYear()}-${String(estimatedOriginal.getMonth() + 1).padStart(2, '0')}`;
    }

    // Get exclusion reason for refunds
    getRefundExclusionReason(refund) {
        if (!refund.isEUDestination) return 'Non-EU destination';
        if (!refund.isSignificantAmount) return 'Insignificant amount';
        if (!refund.hasValidDate) return 'Invalid or missing date';
        return 'Unknown exclusion reason';
    }

    // Group refunds by original reporting period
    groupRefundsByOriginalPeriod(processedRefunds) {
        const refundsByPeriod = {};

        processedRefunds.eligible.forEach(refund => {
            const period = refund.originalPeriod;
            if (!refundsByPeriod[period]) {
                refundsByPeriod[period] = [];
            }
            refundsByPeriod[period].push(refund);
        });

        return refundsByPeriod;
    }

    // Calculate adjustment amounts for each period
    calculateRefundAdjustments(refundsByPeriod) {
        const adjustments = {};

        Object.entries(refundsByPeriod).forEach(([period, refunds]) => {
            const totalTaxAmount = refunds.reduce((sum, refund) => sum + refund.taxAmount, 0);
            const totalNetAmount = refunds.reduce((sum, refund) => {
                const vatRate = refund.vatRate || 0;
                const netAmount = refund.totalAmount / (1 + vatRate / 100);
                return sum + netAmount;
            }, 0);

            adjustments[period] = {
                refundCount: refunds.length,
                totalTaxAmount: totalTaxAmount,
                totalNetAmount: totalNetAmount,
                countries: [...new Set(refunds.map(r => r.country))],
                requiresAdjustment: totalTaxAmount > 0.01
            };
        });

        return adjustments;
    }

    // CRITICAL FIX: Extract partial refund information from Shopify orders
    extractRefundInfo(order) {
        const refundedAmount = this.parseAmount(order['Refunded Amount'] || 0);
        const outstandingBalance = this.parseAmount(order['Outstanding Balance'] || 0);
        const financialStatus = (order['Financial Status'] || '').toLowerCase();

        // Determine refund status
        const hasPartialRefund = refundedAmount > 0;
        const isFullyRefunded = financialStatus === 'refunded';
        const isPartiallyRefunded = hasPartialRefund && !isFullyRefunded;

        // Calculate original amounts before refund
        const currentTotal = this.parseAmount(order.Total || 0);
        const originalTotal = currentTotal + refundedAmount;

        // Calculate refund percentage
        const refundPercentage = originalTotal > 0 ? (refundedAmount / originalTotal) * 100 : 0;

        return {
            refundedAmount,
            outstandingBalance,
            financialStatus,
            hasPartialRefund,
            isFullyRefunded,
            isPartiallyRefunded,
            originalTotal,
            currentTotal,
            refundPercentage,
            // CRITICAL: For IOSS, we need the net effect
            effectiveTotal: currentTotal, // What should be reported for IOSS
            refundImpact: this.calculateRefundImpact(order, refundedAmount)
        };
    }

    // Calculate the impact of partial refunds on IOSS reporting
    calculateRefundImpact(order, refundedAmount) {
        if (refundedAmount <= 0) {
            return {
                hasImpact: false,
                adjustedNetValue: null,
                adjustedVATAmount: null,
                note: 'No refund impact'
            };
        }

        const originalTotal = this.parseAmount(order.Total || 0) + refundedAmount;
        const currentTotal = this.parseAmount(order.Total || 0);
        const originalTaxes = this.parseAmount(order.Taxes || 0);

        // Calculate proportional tax refund
        const refundProportion = refundedAmount / originalTotal;
        const refundedTaxes = originalTaxes * refundProportion;

        // Calculate adjusted amounts for IOSS
        const adjustedTaxes = originalTaxes - refundedTaxes;
        const adjustedSubtotal = this.parseAmount(order.Subtotal || 0) * (1 - refundProportion);

        return {
            hasImpact: true,
            refundProportion,
            refundedTaxes,
            adjustedNetValue: adjustedSubtotal,
            adjustedVATAmount: adjustedTaxes,
            adjustedTotal: currentTotal,
            note: `Partial refund of ${(refundProportion * 100).toFixed(1)}% applied`,
            originalAmounts: {
                total: originalTotal,
                taxes: originalTaxes,
                subtotal: this.parseAmount(order.Subtotal || 0) / (1 - refundProportion)
            }
        };
    }

    // CRITICAL: Apply refund adjustments to financial data
    applyRefundAdjustments(financialData, refundInfo) {
        if (!refundInfo.hasPartialRefund) {
            // No refund, return original data
            return {
                ...financialData,
                isAdjustedForRefunds: false,
                refundAdjustment: null
            };
        }

        if (refundInfo.isFullyRefunded) {
            // Fully refunded order should be excluded from IOSS
            return {
                ...financialData,
                netValue: 0,
                grossValue: 0,
                vatAmount: 0,
                subtotal: 0,
                taxes: 0,
                total: 0,
                isAdjustedForRefunds: true,
                refundAdjustment: {
                    type: 'FULL_REFUND',
                    note: 'Order fully refunded - excluded from IOSS',
                    originalAmounts: {
                        netValue: financialData.netValue,
                        grossValue: financialData.grossValue,
                        vatAmount: financialData.vatAmount
                    }
                }
            };
        }

        // Partial refund - adjust amounts proportionally
        const impact = refundInfo.refundImpact;
        if (impact.hasImpact) {
            const adjustmentFactor = 1 - impact.refundProportion;

            return {
                ...financialData,
                netValue: financialData.netValue * adjustmentFactor,
                grossValue: financialData.grossValue * adjustmentFactor,
                vatAmount: financialData.vatAmount * adjustmentFactor,
                subtotal: financialData.subtotal * adjustmentFactor,
                taxes: financialData.taxes * adjustmentFactor,
                total: financialData.total * adjustmentFactor,
                isAdjustedForRefunds: true,
                refundAdjustment: {
                    type: 'PARTIAL_REFUND',
                    refundPercentage: refundInfo.refundPercentage,
                    adjustmentFactor,
                    refundedAmount: refundInfo.refundedAmount,
                    note: `Partial refund of ${refundInfo.refundPercentage.toFixed(1)}% applied`,
                    originalAmounts: {
                        netValue: financialData.netValue,
                        grossValue: financialData.grossValue,
                        vatAmount: financialData.vatAmount
                    }
                }
            };
        }

        return {
            ...financialData,
            isAdjustedForRefunds: false,
            refundAdjustment: null
        };
    }

    // Display detailed refunds breakdown
    displayRefundsBreakdown() {
        if (!this.processedRefunds) return;

        // Update period breakdown table
        const periodTableBody = document.getElementById('refunds-period-breakdown');
        if (periodTableBody) {
            periodTableBody.innerHTML = '';

            Object.entries(this.processedRefunds.adjustmentCalculations).forEach(([period, adjustment]) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${period}</td>
                    <td>${adjustment.refundCount}</td>
                    <td>${adjustment.countries.length} (${adjustment.countries.join(', ')})</td>
                    <td>€${adjustment.totalTaxAmount.toFixed(2)}</td>
                `;
                periodTableBody.appendChild(row);
            });
        }

        // Update country breakdown table
        const countryTableBody = document.getElementById('refunds-country-breakdown');
        if (countryTableBody) {
            countryTableBody.innerHTML = '';

            // Group refunds by country and VAT rate
            const countryBreakdown = {};
            this.processedRefunds.eligibleRefunds.forEach(refund => {
                const key = `${refund.country}-${refund.vatRate}`;
                if (!countryBreakdown[key]) {
                    countryBreakdown[key] = {
                        country: refund.country,
                        vatRate: refund.vatRate,
                        count: 0,
                        totalAmount: 0
                    };
                }
                countryBreakdown[key].count++;
                countryBreakdown[key].totalAmount += refund.taxAmount;
            });

            Object.values(countryBreakdown).forEach(data => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${data.country}</td>
                    <td>${data.vatRate.toFixed(1)}%</td>
                    <td>${data.count}</td>
                    <td>€${data.totalAmount.toFixed(2)}</td>
                `;
                countryTableBody.appendChild(row);
            });
        }
    }

    // Export functions - FIXED: All export functions now properly implemented

    exportCSV() {
        try {
            const csvContent = this.generateCSVContent();
            this.downloadFile(csvContent, 'ioss-orders-report.csv', 'text/csv');
            console.log('✅ CSV export completed');
        } catch (error) {
            this.addIssue('error', `CSV export failed: ${error.message}`);
        }
    }

    exportIOSSCompliant() {
        try {
            const csvContent = this.generateIOSSCompliantCSV();
            this.downloadFile(csvContent, 'ioss-compliant-report.csv', 'text/csv');
            console.log('✅ IOSS compliant export completed');
        } catch (error) {
            this.addIssue('error', `IOSS compliant export failed: ${error.message}`);
        }
    }

    exportDetailed() {
        try {
            const content = this.generateDetailedReport();
            this.downloadFile(content, 'detailed-report.txt', 'text/plain');
            console.log('✅ Detailed export completed');
        } catch (error) {
            this.addIssue('error', `Detailed export failed: ${error.message}`);
        }
    }

    exportXML() {
        try {
            const xmlContent = this.generateXMLReport();
            this.downloadFile(xmlContent, 'ioss-report.xml', 'application/xml');
            console.log('✅ XML export completed');
        } catch (error) {
            this.addIssue('error', `XML export failed: ${error.message}`);
        }
    }

    exportEstonianVATXML() {
        try {
            const xmlContent = this.generateEstonianVATXML();
            this.downloadFile(xmlContent, 'estonian-vat-report.xml', 'application/xml');
            console.log('✅ Estonian VAT XML export completed');
        } catch (error) {
            this.addIssue('error', `Estonian VAT XML export failed: ${error.message}`);
        }
    }

    // FIXED: Refunds export functions - These were completely missing!
    exportRefundsCSV() {
        try {
            if (!this.processedRefunds) {
                throw new Error('No refunds data available. Please process refunds first.');
            }
            const csvContent = this.generateRefundsCSV();
            this.downloadFile(csvContent, 'refunds-report.csv', 'text/csv');
            console.log('✅ Refunds CSV export completed');
        } catch (error) {
            this.addIssue('error', `Refunds CSV export failed: ${error.message}`);
        }
    }

    exportRefundsXML() {
        try {
            if (!this.processedRefunds) {
                throw new Error('No refunds data available. Please process refunds first.');
            }
            const xmlContent = this.generateRefundsXML();
            this.downloadFile(xmlContent, 'refunds-report.xml', 'application/xml');
            console.log('✅ Refunds XML export completed');
        } catch (error) {
            this.addIssue('error', `Refunds XML export failed: ${error.message}`);
        }
    }

    exportRefundsDetailed() {
        try {
            if (!this.processedRefunds) {
                throw new Error('No refunds data available. Please process refunds first.');
            }
            const content = this.generateRefundsDetailedReport();
            this.downloadFile(content, 'refunds-detailed-report.txt', 'text/plain');
            console.log('✅ Refunds detailed export completed');
        } catch (error) {
            this.addIssue('error', `Refunds detailed export failed: ${error.message}`);
        }
    }

    // Content generation functions
    generateCSVContent() {
        let csv = 'Country,VATRate,Orders,NetValue,VATAmount,Currency,Period\n';
        
        Object.values(this.processedData.breakdown).forEach(data => {
            csv += `${data.country},${data.vatRate},${data.orders},${data.netValue.toFixed(2)},${data.vatAmount.toFixed(2)},${this.companyInfo.currency},${this.companyInfo.period}\n`;
        });
        
        return csv;
    }

    generateIOSSCompliantCSV() {
        let csv = 'CountryCode,SupplyType,VATRateType,VATRate,TaxableAmount,VATAmount,Currency,ReportingPeriod\n';
        
        Object.values(this.processedData.breakdown).forEach(data => {
            const rateType = this.determineVATRateType(data.vatRate, data.country);
            csv += `${data.country},GOODS,${rateType},${data.vatRate},${data.netValue.toFixed(2)},${data.vatAmount.toFixed(2)},${this.companyInfo.currency},${this.companyInfo.period}\n`;
        });
        
        return csv;
    }

    determineVATRateType(rate, country) {
        // Enhanced VAT rate categorization with country-specific rules
        const countryRates = this.getCountryVATRates(country);

        if (!countryRates) {
            // Fallback to basic categorization
            if (rate >= 20) return 'STANDARD';
            if (rate >= 10) return 'REDUCED';
            if (rate >= 5) return 'SUPER_REDUCED';
            return 'PARKING';
        }

        // Check against country-specific rates
        if (Math.abs(rate - countryRates.standard) < 0.1) return 'STANDARD';

        // Check reduced rates
        if (countryRates.reduced) {
            for (const reducedRate of countryRates.reduced) {
                if (Math.abs(rate - reducedRate) < 0.1) return 'REDUCED';
            }
        }

        // Check super reduced rates
        if (countryRates.superReduced) {
            for (const superReducedRate of countryRates.superReduced) {
                if (Math.abs(rate - superReducedRate) < 0.1) return 'SUPER_REDUCED';
            }
        }

        // Check parking rate
        if (countryRates.parking && Math.abs(rate - countryRates.parking) < 0.1) {
            return 'PARKING';
        }

        // Check zero rate
        if (rate === 0) return 'ZERO';

        // Default fallback
        if (rate >= 20) return 'STANDARD';
        if (rate >= 10) return 'REDUCED';
        if (rate >= 5) return 'SUPER_REDUCED';
        return 'PARKING';
    }

    // Enhanced country-specific VAT rates with regional variations
    getCountryVATRates(countryCode, postalCode = '') {
        const countryVATRates = {
            'AT': {
                standard: 20,
                reduced: [10, 13],
                superReduced: [],
                parking: 0,
                regions: {
                    // Jungholz and Mittelberg (German customs territory)
                    '6691': { standard: 19, reduced: [7], note: 'German customs territory' },
                    '6991': { standard: 19, reduced: [7], note: 'German customs territory' }
                }
            },
            'BE': { standard: 21, reduced: [6, 12], superReduced: [], parking: 0 },
            'BG': { standard: 20, reduced: [9], superReduced: [], parking: 0 },
            'HR': { standard: 25, reduced: [5, 13], superReduced: [], parking: 0 },
            'CY': { standard: 19, reduced: [5, 9], superReduced: [], parking: 0 },
            'CZ': { standard: 21, reduced: [10, 15], superReduced: [], parking: 0 },
            'DK': { standard: 25, reduced: [], superReduced: [], parking: 0 },
            'EE': { standard: 22, reduced: [9], superReduced: [], parking: 0 },
            'FI': {
                standard: 25.5,
                reduced: [10, 14],
                superReduced: [],
                parking: 0,
                regions: {
                    // Åland Islands
                    '22': { standard: 25.5, reduced: [10, 14], note: 'Åland Islands' }
                }
            },
            'FR': {
                standard: 20,
                reduced: [5.5, 10],
                superReduced: [2.1],
                parking: 0,
                regions: {
                    // Corsica
                    '20': { standard: 20, reduced: [10], superReduced: [2.1], note: 'Corsica' }
                }
            },
            'DE': { standard: 19, reduced: [7], superReduced: [], parking: 0 },
            'GR': {
                standard: 24,
                reduced: [6, 13],
                superReduced: [],
                parking: 0,
                regions: {
                    // Greek islands (reduced rates for some)
                    '84': { standard: 17, reduced: [6, 9], note: 'Dodecanese islands' },
                    '85': { standard: 17, reduced: [6, 9], note: 'Cyclades islands' }
                }
            },
            'EL': { standard: 24, reduced: [6, 13], superReduced: [], parking: 0 },
            'HU': { standard: 27, reduced: [5, 18], superReduced: [], parking: 0 },
            'IE': { standard: 23, reduced: [9, 13.5], superReduced: [4.8], parking: 0 },
            'IT': {
                standard: 22,
                reduced: [5, 10],
                superReduced: [4],
                parking: 0,
                regions: {
                    // South Tyrol (German-speaking)
                    '39': { standard: 22, reduced: [5, 10], note: 'South Tyrol' }
                }
            },
            'LV': { standard: 21, reduced: [5, 12], superReduced: [], parking: 0 },
            'LT': { standard: 21, reduced: [5, 9], superReduced: [], parking: 0 },
            'LU': { standard: 17, reduced: [8], superReduced: [3], parking: 14 },
            'MT': { standard: 18, reduced: [5, 7], superReduced: [], parking: 0 },
            'NL': { standard: 21, reduced: [9], superReduced: [], parking: 0 },
            'PL': { standard: 23, reduced: [5, 8], superReduced: [], parking: 0 },
            'PT': {
                standard: 23,
                reduced: [6, 13],
                superReduced: [],
                parking: 0,
                regions: {
                    // Azores
                    '95': { standard: 18, reduced: [5, 9], note: 'Azores' },
                    '96': { standard: 18, reduced: [5, 9], note: 'Azores' },
                    '97': { standard: 18, reduced: [5, 9], note: 'Azores' },
                    // Madeira
                    '90': { standard: 22, reduced: [5, 12], note: 'Madeira' },
                    '91': { standard: 22, reduced: [5, 12], note: 'Madeira' },
                    '92': { standard: 22, reduced: [5, 12], note: 'Madeira' },
                    '93': { standard: 22, reduced: [5, 12], note: 'Madeira' },
                    '94': { standard: 22, reduced: [5, 12], note: 'Madeira' }
                }
            },
            'RO': { standard: 19, reduced: [5, 9], superReduced: [], parking: 0 },
            'SK': { standard: 23, reduced: [10], superReduced: [], parking: 0 },
            'SI': { standard: 22, reduced: [5, 9.5], superReduced: [], parking: 0 },
            'ES': {
                standard: 21,
                reduced: [10],
                superReduced: [4],
                parking: 0,
                regions: {
                    // Ceuta and Melilla
                    '51': { standard: 0, reduced: [0], note: 'Ceuta - IPSI tax system' },
                    '52': { standard: 0, reduced: [0], note: 'Melilla - IPSI tax system' }
                }
            },
            'SE': { standard: 25, reduced: [6, 12], superReduced: [], parking: 0 },
            'XI': {
                standard: 20,
                reduced: [5],
                superReduced: [],
                parking: 0,
                note: 'Northern Ireland - follows EU IOSS rules post-Brexit',
                specialRules: {
                    goods: 'EU IOSS applies',
                    services: 'May require UK VAT treatment',
                    digital: 'Complex rules - check specific guidance'
                }
            }
        };

        const countryRates = countryVATRates[countryCode];
        if (!countryRates) return null;

        // Check for regional variations based on postal code
        if (countryRates.regions && postalCode) {
            for (const [regionCode, regionRates] of Object.entries(countryRates.regions)) {
                if (postalCode.startsWith(regionCode)) {
                    console.log(`📍 Using regional VAT rates for ${countryCode}-${regionCode}: ${regionRates.note}`);
                    return {
                        ...countryRates,
                        ...regionRates,
                        isRegional: true,
                        regionNote: regionRates.note
                    };
                }
            }
        }

        return countryRates;
    }

    // ENHANCED: Comprehensive product-specific VAT rate logic
    getApplicableVATRate(country, postalCode, productType, extractedRate, productCategory = '') {
        const countryRates = this.getCountryVATRates(country, postalCode);
        if (!countryRates) return extractedRate || this.getDefaultVATRate(country);

        // If we have a valid extracted rate, use it (but validate it's reasonable)
        if (extractedRate && extractedRate > 0 && this.isValidVATRate(extractedRate, countryRates)) {
            return extractedRate;
        }

        // Determine product category and apply specific rates
        const category = this.categorizeProduct(productType, productCategory);
        return this.getVATRateForCategory(category, countryRates, country);
    }

    // Comprehensive product categorization
    categorizeProduct(productName, productCategory) {
        const name = (productName || '').toLowerCase();
        const category = (productCategory || '').toLowerCase();

        // Books and publications
        if (this.matchesKeywords(name, ['book', 'magazine', 'newspaper', 'journal', 'publication', 'manual', 'guide', 'atlas', 'dictionary']) ||
            this.matchesKeywords(category, ['books', 'publications', 'media'])) {
            return 'books';
        }

        // Food and beverages
        if (this.matchesKeywords(name, ['food', 'drink', 'beverage', 'coffee', 'tea', 'water', 'juice', 'snack', 'meal', 'nutrition']) ||
            this.matchesKeywords(category, ['food', 'beverages', 'grocery'])) {
            return 'food';
        }

        // Medical and pharmaceutical
        if (this.matchesKeywords(name, ['medical', 'medicine', 'pharmaceutical', 'health', 'vitamin', 'supplement', 'therapy', 'treatment', 'drug']) ||
            this.matchesKeywords(category, ['medical', 'health', 'pharmacy'])) {
            return 'medical';
        }

        // Children's items
        if (this.matchesKeywords(name, ['child', 'baby', 'kid', 'infant', 'toddler', 'toy', 'diaper', 'stroller', 'crib']) ||
            this.matchesKeywords(category, ['children', 'baby', 'toys'])) {
            return 'children';
        }

        // Cultural items (museums, theaters, etc.)
        if (this.matchesKeywords(name, ['ticket', 'museum', 'theater', 'concert', 'exhibition', 'cultural', 'art', 'performance']) ||
            this.matchesKeywords(category, ['culture', 'entertainment', 'tickets'])) {
            return 'culture';
        }

        // Transport and accommodation
        if (this.matchesKeywords(name, ['transport', 'taxi', 'bus', 'train', 'flight', 'hotel', 'accommodation', 'travel']) ||
            this.matchesKeywords(category, ['transport', 'travel', 'accommodation'])) {
            return 'transport';
        }

        // Digital services
        if (this.matchesKeywords(name, ['digital', 'software', 'app', 'download', 'streaming', 'subscription', 'online', 'cloud']) ||
            this.matchesKeywords(category, ['digital', 'software', 'services'])) {
            return 'digital';
        }

        // Energy and utilities
        if (this.matchesKeywords(name, ['energy', 'electricity', 'gas', 'heating', 'utility', 'power']) ||
            this.matchesKeywords(category, ['energy', 'utilities'])) {
            return 'energy';
        }

        // Clothing and textiles
        if (this.matchesKeywords(name, ['clothing', 'shirt', 'dress', 'shoes', 'jacket', 'pants', 'textile', 'fashion']) ||
            this.matchesKeywords(category, ['clothing', 'fashion', 'textiles'])) {
            return 'clothing';
        }

        // Electronics
        if (this.matchesKeywords(name, ['electronic', 'computer', 'phone', 'tablet', 'device', 'gadget', 'tech']) ||
            this.matchesKeywords(category, ['electronics', 'technology'])) {
            return 'electronics';
        }

        // Default category
        return 'standard';
    }

    // Helper method to match keywords
    matchesKeywords(text, keywords) {
        return keywords.some(keyword => text.includes(keyword));
    }

    // Get VAT rate for specific product category by country
    getVATRateForCategory(category, countryRates, countryCode) {
        // Country-specific product VAT rate rules
        const countrySpecificRules = {
            'DE': {
                'books': 7,
                'food': 7,
                'medical': 7,
                'children': 7,
                'culture': 7,
                'transport': 7,
                'digital': 19,
                'energy': 19,
                'standard': 19
            },
            'FR': {
                'books': 5.5,
                'food': 5.5,
                'medical': 2.1,
                'children': 5.5,
                'culture': 5.5,
                'transport': 10,
                'digital': 20,
                'energy': 20,
                'standard': 20
            },
            'IT': {
                'books': 4,
                'food': 4,
                'medical': 4,
                'children': 4,
                'culture': 10,
                'transport': 10,
                'digital': 22,
                'energy': 10,
                'standard': 22
            },
            'ES': {
                'books': 4,
                'food': 4,
                'medical': 4,
                'children': 4,
                'culture': 10,
                'transport': 10,
                'digital': 21,
                'energy': 21,
                'standard': 21
            },
            'NL': {
                'books': 9,
                'food': 9,
                'medical': 9,
                'children': 9,
                'culture': 9,
                'transport': 9,
                'digital': 21,
                'energy': 21,
                'standard': 21
            },
            'XI': {
                'books': 5,
                'food': 5,
                'medical': 5,
                'children': 5,
                'culture': 5,
                'transport': 5,
                'digital': 20,
                'energy': 20,
                'standard': 20
            }
        };

        // Use country-specific rules if available
        const countryRules = countrySpecificRules[countryCode];
        if (countryRules && countryRules[category]) {
            return countryRules[category];
        }

        // Fallback to general category mapping
        switch (category) {
            case 'books':
            case 'food':
            case 'children':
                return countryRates.reduced?.[0] || countryRates.standard;

            case 'medical':
                return countryRates.superReduced?.[0] || countryRates.reduced?.[0] || countryRates.standard;

            case 'culture':
            case 'transport':
                return countryRates.reduced?.[1] || countryRates.reduced?.[0] || countryRates.standard;

            case 'digital':
            case 'electronics':
            case 'energy':
            default:
                return countryRates.standard;
        }
    }

    // Validate if extracted VAT rate is reasonable for the country
    isValidVATRate(rate, countryRates) {
        const allRates = [
            countryRates.standard,
            ...(countryRates.reduced || []),
            ...(countryRates.superReduced || []),
            countryRates.parking || 0,
            0 // Zero rate is always valid
        ].filter(r => r !== undefined);

        // Allow 0.5% tolerance for rate matching
        return allRates.some(validRate => Math.abs(rate - validRate) <= 0.5);
    }

    generateDetailedReport() {
        let report = `IOSS Detailed Report\n`;
        report += `${'='.repeat(50)}\n`;
        report += `Generated: ${new Date().toISOString()}\n`;
        report += `Company: ${this.companyInfo.name}\n`;
        report += `IOSS Number: ${this.companyInfo.iossNumber}\n`;
        report += `Reporting Period: ${this.companyInfo.period}\n`;
        report += `Base Currency: ${this.companyInfo.currency}\n\n`;
        
        const summary = this.processedData.summary;
        report += `SUMMARY\n`;
        report += `${'='.repeat(30)}\n`;
        report += `Total Orders Processed: ${summary.totalOrders}\n`;
        report += `IOSS Eligible Orders: ${summary.iossEligibleOrders}\n`;
        report += `Excluded Orders: ${summary.excludedOrders}\n`;
        report += `Total VAT Collected: ${this.getCurrencySymbol(this.companyInfo.currency)}${summary.totalVatCalculated.toFixed(2)}\n`;
        report += `Total Value (EUR): €${summary.totalValueEUR.toFixed(2)}\n`;
        report += `Countries Served: ${summary.countryCount}\n\n`;
        
        report += `BREAKDOWN BY COUNTRY AND VAT RATE\n`;
        report += `${'='.repeat(40)}\n`;
        Object.values(this.processedData.breakdown).forEach(data => {
            report += `${data.country} (${data.vatRate.toFixed(1)}%): ${data.orders} orders, `;
            report += `€${data.netValue.toFixed(2)} net, €${data.vatAmount.toFixed(2)} VAT\n`;
        });
        
        if (this.issues.length > 0) {
            report += `\nISSUES AND WARNINGS\n`;
            report += `${'='.repeat(30)}\n`;
            this.issues.forEach(issue => {
                report += `[${issue.type.toUpperCase()}] ${issue.message}\n`;
            });
        }
        
        return report;
    }

    generateXMLReport() {
        let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
        xml += '<IOSSReport>\n';
        xml += `  <CompanyInfo>\n`;
        xml += `    <Name>${this.escapeXML(this.companyInfo.name)}</Name>\n`;
        xml += `    <IOSSNumber>${this.companyInfo.iossNumber}</IOSSNumber>\n`;
        xml += `    <ReportingPeriod>${this.companyInfo.period}</ReportingPeriod>\n`;
        xml += `    <Currency>${this.companyInfo.currency}</Currency>\n`;
        xml += `  </CompanyInfo>\n`;
        xml += `  <Summary>\n`;
        xml += `    <TotalOrders>${this.processedData.summary.totalOrders}</TotalOrders>\n`;
        xml += `    <EligibleOrders>${this.processedData.summary.iossEligibleOrders}</EligibleOrders>\n`;
        xml += `    <TotalVAT>${this.processedData.summary.totalVatCalculated.toFixed(2)}</TotalVAT>\n`;
        xml += `    <CountriesCount>${this.processedData.summary.countryCount}</CountriesCount>\n`;
        xml += `  </Summary>\n`;
        xml += '  <VATReturns>\n';
        
        Object.values(this.processedData.breakdown).forEach(data => {
            xml += '    <VATReturn>\n';
            xml += `      <Country>${data.country}</Country>\n`;
            xml += `      <VATRate>${data.vatRate.toFixed(2)}</VATRate>\n`;
            xml += `      <Orders>${data.orders}</Orders>\n`;
            xml += `      <TaxableAmount>${data.netValue.toFixed(2)}</TaxableAmount>\n`;
            xml += `      <VATAmount>${data.vatAmount.toFixed(2)}</VATAmount>\n`;
            xml += '    </VATReturn>\n';
        });
        
        xml += '  </VATReturns>\n';
        xml += '</IOSSReport>';
        
        return xml;
    }

    generateEstonianVATXML() {
        const [year, month] = this.companyInfo.period.split('-');

        let xml = '<?xml version="1.0" encoding="UTF-8"?>';
        xml += '<ReturnsInformations xsi:noNamespaceSchemaLocation="OSS%20IOSS%20kliendifaili%20formaat.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">';
        xml += '<ReturnsInformation>';
        xml += '<SchemaType>IMPORT</SchemaType>';
        xml += '<TraderID>';
        xml += `<IOSSNumber issuedBy="EE">${this.companyInfo.iossNumber}</IOSSNumber>`;
        xml += '</TraderID>';
        xml += '<Period>';
        xml += `<Year>${year}</Year>`;
        xml += `<Month>${month.padStart(2, '0')}</Month>`;
        xml += '</Period>';

        // Sort by country code for consistent output
        const sortedBreakdown = Object.values(this.processedData.breakdown)
            .sort((a, b) => a.country.localeCompare(b.country));

        sortedBreakdown.forEach(data => {
            // Only include entries with significant amounts
            if (data.netValue > 0.01 && data.vatAmount > 0.01) {
                xml += '<VATReturn>';
                xml += `<MSCONCountryCode>${data.country}</MSCONCountryCode>`;
                xml += '<SupplyType>GOODS</SupplyType>';
                xml += `<VATRate type="STANDARD">${data.vatRate.toFixed(2)}</VATRate>`;
                xml += `<TaxableAmount>${data.netValue.toFixed(2)}</TaxableAmount>`;
                xml += `<VATAmount>${data.vatAmount.toFixed(2)}</VATAmount>`;
                xml += '</VATReturn>';
            }
        });

        xml += '</ReturnsInformation>';
        xml += '</ReturnsInformations>';

        return xml;
    }

    generateRefundsCSV() {
        let csv = 'OrderID,Country,VATRate,RefundAmount,OriginalPeriod,RefundDate,Currency\n';
        
        this.processedRefunds.eligibleRefunds.forEach(refund => {
            const orderId = refund['Order ID'] || refund.Name || 'Unknown';
            const country = this.extractCountry(refund);
            const vatRate = this.extractVATRate(refund);
            const refundAmount = Math.abs(parseFloat(refund['Tax amount'] || refund['Tax Amount'] || 0));
            const originalPeriod = refund.Day?.substring(0, 7) || 'Unknown';
            const refundDate = refund.Day || refund.Date || 'Unknown';
            
            csv += `${orderId},${country},${vatRate},${refundAmount.toFixed(2)},${originalPeriod},${refundDate},EUR\n`;
        });
        
        return csv;
    }

    generateRefundsXML() {
        let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
        xml += '<RefundsReport>\n';
        xml += `  <CompanyInfo>\n`;
        xml += `    <Name>${this.escapeXML(this.companyInfo.name)}</Name>\n`;
        xml += `    <IOSSNumber>${this.companyInfo.iossNumber}</IOSSNumber>\n`;
        xml += `  </CompanyInfo>\n`;
        xml += `  <GeneratedDate>${new Date().toISOString()}</GeneratedDate>\n`;
        xml += '  <Refunds>\n';
        
        this.processedRefunds.eligibleRefunds.forEach(refund => {
            xml += '    <Refund>\n';
            xml += `      <OrderID>${this.escapeXML(refund['Order ID'] || refund.Name || 'Unknown')}</OrderID>\n`;
            xml += `      <Country>${this.extractCountry(refund)}</Country>\n`;
            xml += `      <VATRate>${this.extractVATRate(refund)}</VATRate>\n`;
            xml += `      <RefundAmount>${Math.abs(parseFloat(refund['Tax amount'] || refund['Tax Amount'] || 0)).toFixed(2)}</RefundAmount>\n`;
            xml += `      <OriginalPeriod>${refund.Day?.substring(0, 7) || 'Unknown'}</OriginalPeriod>\n`;
            xml += `      <RefundDate>${refund.Day || refund.Date || 'Unknown'}</RefundDate>\n`;
            xml += '    </Refund>\n';
        });
        
        xml += '  </Refunds>\n';
        xml += '</RefundsReport>';
        
        return xml;
    }

    generateRefundsDetailedReport() {
        let report = `IOSS Refunds Detailed Report\n`;
        report += `${'='.repeat(50)}\n`;
        report += `Generated: ${new Date().toISOString()}\n`;
        report += `Company: ${this.companyInfo.name}\n`;
        report += `IOSS Number: ${this.companyInfo.iossNumber}\n\n`;
        
        const totalRefundAmount = this.processedRefunds.eligibleRefunds.reduce((sum, r) => 
            sum + Math.abs(parseFloat(r['Tax amount'] || r['Tax Amount'] || 0)), 0);
        
        report += `REFUNDS SUMMARY\n`;
        report += `${'='.repeat(30)}\n`;
        report += `Total Refunds Found: ${this.processedRefunds.allRefunds.length}\n`;
        report += `IOSS Eligible Refunds: ${this.processedRefunds.eligibleRefunds.length}\n`;
        report += `Total Refund Amount: €${totalRefundAmount.toFixed(2)}\n`;
        report += `Periods Covered: ${this.processedRefunds.periods.length}\n`;
        report += `Periods: ${this.processedRefunds.periods.join(', ')}\n\n`;
        
        report += `DETAILED REFUNDS LIST\n`;
        report += `${'='.repeat(30)}\n`;
        this.processedRefunds.eligibleRefunds.forEach(refund => {
            const orderId = refund['Order ID'] || refund.Name || 'Unknown';
            const amount = Math.abs(parseFloat(refund['Tax amount'] || refund['Tax Amount'] || 0));
            const country = this.extractCountry(refund);
            const period = refund.Day?.substring(0, 7) || 'Unknown';
            
            report += `Order: ${orderId}, Country: ${country}, Amount: €${amount.toFixed(2)}, Period: ${period}\n`;
        });
        
        return report;
    }

    // Utility functions
    escapeXML(text) {
        return text.toString()
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }

    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // UI helper functions
    updateElement(id, content) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = content;
        }
    }

    getElementValue(id) {
        const element = document.getElementById(id);
        return element ? element.value : '';
    }

    showElement(id) {
        const element = document.getElementById(id);
        if (element) {
            element.style.display = 'block';
        }
    }

    hideElement(id) {
        const element = document.getElementById(id);
        if (element) {
            element.style.display = 'none';
        }
    }

    showProcessingStatus(message) {
        const statusTextElement = document.getElementById('status-text');
        const spinnerElement = document.querySelector('#processing-status .spinner');
        const resultsSection = document.getElementById('results-section');

        if (statusTextElement) {
            statusTextElement.textContent = message;
        }
        if (spinnerElement) {
            spinnerElement.style.display = 'block';
        }
        if (resultsSection) {
            resultsSection.style.display = 'block';
        }
    }

    hideProcessingStatus() {
        const spinnerElement = document.querySelector('#processing-status .spinner');
        const resultsContent = document.getElementById('results-content');

        if (spinnerElement) {
            spinnerElement.style.display = 'none';
        }
        if (resultsContent) {
            resultsContent.style.display = 'block';
        }
    }

    showRefundsProcessingStatus(message) {
        const statusTextElement = document.getElementById('refunds-status-text');
        const spinnerElement = document.querySelector('#refunds-processing-status .spinner');
        const resultsSection = document.getElementById('refunds-results-section');

        if (statusTextElement) {
            statusTextElement.textContent = message;
        }
        if (spinnerElement) {
            spinnerElement.style.display = 'block';
        }
        if (resultsSection) {
            resultsSection.style.display = 'block';
        }
    }

    hideRefundsProcessingStatus() {
        const spinnerElement = document.querySelector('#refunds-processing-status .spinner');
        const resultsContent = document.getElementById('refunds-results-content');

        if (spinnerElement) {
            spinnerElement.style.display = 'none';
        }
        if (resultsContent) {
            resultsContent.style.display = 'block';
        }
    }

    addIssue(type, message) {
        this.issues.push({ type, message, timestamp: new Date().toISOString() });
        console.log(`[${type.toUpperCase()}] ${message}`);
    }

    displayIssues() {
        const issuesContainer = document.getElementById('processing-issues');
        if (!issuesContainer) return;

        if (this.issues.length === 0) {
            issuesContainer.innerHTML = '<p class="success">✅ No issues found during processing</p>';
            return;
        }

        issuesContainer.innerHTML = '';
        this.issues.forEach(issue => {
            const issueElement = document.createElement('div');
            issueElement.className = `issue-item ${issue.type}`;
            issueElement.innerHTML = `
                <span class="issue-type">[${issue.type.toUpperCase()}]</span>
                <span class="issue-message">${issue.message}</span>
            `;
            issuesContainer.appendChild(issueElement);
        });

        issuesContainer.style.display = 'block';
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    console.log('🌟 Starting IOSS Reporter v2.0...');
    window.iossReporter = new IOSSReporter();
});

// Expose for debugging
if (typeof window !== 'undefined') {
    window.IOSSReporter = IOSSReporter;
}
