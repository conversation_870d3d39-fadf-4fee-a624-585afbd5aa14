<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IOSS Reporter - Shopify to IOSS Report Generator</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>IOSS Reporter</h1>
            <p>Convert Shopify order data to IOSS reporting format</p>
        </header>

        <main>
            <!-- Step 1: File Upload Section -->
            <section id="upload-section" class="card">
                <h2>Step 1: Upload Files</h2>
                
                <!-- Tab Navigation -->
                <div class="tab-navigation">
                    <button class="tab-btn active" id="orders-tab" onclick="switchTab('orders')">
                        📦 Shopify Orders
                    </button>
                    <button class="tab-btn" id="refunds-tab" onclick="switchTab('refunds')">
                        🔄 Tax Report (Refunds)
                    </button>
                </div>

                <!-- Orders Upload Tab -->
                <div id="orders-upload" class="tab-content active">
                    <h3>Upload Shopify Orders CSV</h3>
                    <div class="upload-area" id="upload-area">
                        <div class="upload-content">
                            <div class="upload-icon">📁</div>
                            <p>Drag and drop your Shopify orders CSV files here</p>
                            <p class="upload-note">Support for multiple files - combine all your Shopify stores</p>
                            <p>or</p>
                            <button type="button" id="select-file-btn" class="btn btn-primary" onclick="window.triggerFileUpload && window.triggerFileUpload()">
                                Select Files
                            </button>
                            <button type="button" class="btn btn-warning btn-sm" onclick="alert('HTML button works! Issue is with JavaScript.'); console.log('HTML button clicked');" style="margin-left: 10px;">
                                🧪 Test HTML
                            </button>
                            <input type="file" id="file-input" accept=".csv" multiple style="display: none;">
                        </div>
                    </div>
                    <div id="file-info" class="file-info" style="display: none;">
                        <div id="files-list"></div>
                        <p class="total-summary">Total records from all files: <span id="total-record-count">0</span></p>
                    </div>
                </div>

                <!-- Refunds Upload Tab -->
                <div id="refunds-upload" class="tab-content">
                    <h3>Upload Shopify Tax Report CSV (with Refunds)</h3>
                    <div class="info-box">
                        <p><strong>📋 What is this?</strong></p>
                        <p>This processes Shopify tax reports that contain refund transactions (negative values). 
                        The system will:</p>
                        <ul>
                            <li>✅ Identify refund transactions (negative tax amounts)</li>
                            <li>✅ Exclude refunds from current period reporting</li>
                            <li>✅ Associate refunds with their original reporting periods</li>
                            <li>✅ Generate adjustment reports for previous periods</li>
                        </ul>
                    </div>
                    <div class="upload-area" id="refunds-upload-area">
                        <div class="upload-content">
                            <div class="upload-icon">🔄</div>
                            <p>Drag and drop your Shopify Tax Report CSV here</p>
                            <p class="upload-note">File should contain all transactions including refunds</p>
                            <p>or</p>
                            <button type="button" id="select-refunds-file-btn" class="btn btn-primary">
                                Select Tax Report File
                            </button>
                            <input type="file" id="refunds-file-input" accept=".csv" style="display: none;">
                        </div>
                    </div>
                    <div id="refunds-file-info" class="file-info" style="display: none;">
                        <div id="refunds-files-list"></div>
                        <p class="total-summary">Total transactions: <span id="refunds-record-count">0</span></p>
                        <p class="refunds-summary">Refunds found: <span id="refunds-count">0</span></p>
                    </div>
                    
                    <!-- Refunds Processing Section -->
                    <div id="refunds-process-section" class="process-section" style="display: none;">
                        <h4>Process Refunds for IOSS Reporting</h4>
                        <p>Click below to process the detected refunds and generate IOSS adjustment reports:</p>
                        <button type="button" id="process-refunds-btn" class="btn btn-primary">
                            🔄 Process Refunds for IOSS
                        </button>
                    </div>
                </div>
            </section>

            <!-- Step 2: Company Information -->
            <section id="company-section" class="card" style="display: none;">
                <h2>Step 2: Company Information</h2>
                <form id="company-form">
                    <div class="form-group">
                        <label for="company-name">Company Name:</label>
                        <input type="text" id="company-name" required>
                    </div>
                    <div class="form-group">
                        <label for="ioss-number">IOSS Number:</label>
                        <input type="text" id="ioss-number" placeholder="IM..." required>
                    </div>
                    <div class="form-group">
                        <label for="reporting-period">Reporting Period:</label>
                        <select id="reporting-period" required>
                            <option value="">Select Period</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="currency">Reporting Currency:</label>
                        <select id="currency" required>
                            <option value="EUR">EUR</option>
                            <option value="USD">USD</option>
                            <option value="GBP">GBP</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">Process Data</button>
                </form>
            </section>

            <!-- Step 3: Refunds Processing Results -->
            <section id="refunds-results-section" class="card" style="display: none;">
                <h2>Step 3: Refunds Processing Results</h2>
                <div id="refunds-processing-status" class="status">
                    <div class="spinner" style="display: none;"></div>
                    <p id="refunds-status-text">Ready to process refunds...</p>
                </div>
                
                <!-- Refunds Results Content -->
                <div id="refunds-results-content" style="display: none;">
                    <!-- Refunds Summary Statistics -->
                    <div id="refunds-summary-stats">
                        <h3>Refunds Summary</h3>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="stat-label">Total Refunds Found:</span>
                                <span class="stat-value" id="refunds-total">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Eligible for IOSS:</span>
                                <span class="stat-value" id="refunds-eligible">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Excluded:</span>
                                <span class="stat-value" id="refunds-excluded">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Original Periods:</span>
                                <span class="stat-value" id="refunds-periods">0</span>
                            </div>
                        </div>
                    </div>

                    <!-- Refunds by Period Table -->
                    <div class="refunds-period-breakdown-section">
                        <h3>Refunds by Original Period</h3>
                        <table class="breakdown-table">
                            <thead>
                                <tr>
                                    <th>Original Period</th>
                                    <th>Refunds</th>
                                    <th>Countries</th>
                                    <th>Total Amount (EUR)</th>
                                </tr>
                            </thead>
                            <tbody id="refunds-period-breakdown">
                            </tbody>
                        </table>
                    </div>

                    <!-- Refunds by Country Table -->
                    <div class="refunds-country-breakdown-section">
                        <h3>Refunds by Country & VAT Rate</h3>
                        <table class="breakdown-table">
                            <thead>
                                <tr>
                                    <th>Country</th>
                                    <th>VAT Rate</th>
                                    <th>Refunds</th>
                                    <th>Total Amount (EUR)</th>
                                </tr>
                            </thead>
                            <tbody id="refunds-country-breakdown">
                            </tbody>
                        </table>
                    </div>

                    <!-- Refunds Export Buttons -->
                    <div id="refunds-export-buttons" class="export-section" style="display: none;">
                        <h3>📊 Export Refunds Reports</h3>
                        <div class="export-grid">
                            <button id="export-refunds-csv" class="export-btn">
                                📋 Export Refunds CSV
                            </button>
                            <button id="export-refunds-xml" class="export-btn">
                                🗂️ Export Refunds XML
                            </button>
                            <button id="export-refunds-detailed" class="export-btn">
                                📄 Export Detailed Refunds Report
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Step 4: Orders Processing Results -->
            <section id="results-section" class="card" style="display: none;">
                <h2>Step 4: Orders Processing Results</h2>
                <div id="processing-status" class="status">
                    <div class="spinner" style="display: none;"></div>
                    <p id="status-text">Ready to process...</p>
                </div>
                
                <!-- Processing Results Content -->
                <div id="results-content" style="display: none;">
                    <!-- Summary Statistics -->
                    <div id="summary-stats">
                        <h3>Summary</h3>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="stat-label">Total Orders:</span>
                                <span class="stat-value" id="total-orders">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">IOSS Eligible Orders:</span>
                                <span class="stat-value" id="ioss-orders">0</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Total VAT Collected (Calculated):</span>
                                <span class="stat-value" id="total-vat-calculated">€0.00</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Total VAT Collected (Shopify):</span>
                                <span class="stat-value" id="total-vat-shopify">€0.00</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">VAT Difference (Calc - Shopify):</span>
                                <span class="stat-value" id="vat-difference">€0.00</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Countries:</span>
                                <span class="stat-value" id="countries-count">0</span>
                            </div>
                        </div>
                    </div>

                    <!-- Country Breakdown Table -->
                    <div class="country-breakdown-section">
                        <h3>Country & VAT Rate Breakdown</h3>
                        <p class="breakdown-description">Each row represents a different VAT rate applied in each country.</p>
                    <table class="breakdown-table">
                        <thead>
                            <tr>
                                <th>Country</th>
                                <th>VAT Rate</th>
                                <th>Orders</th>
                                <th>Net Value (Calculated)</th>
                                <th>VAT Amount (Calculated)</th>
                                <th>Net Value (Shopify)</th>
                                <th>VAT Amount (Shopify)</th>
                            </tr>
                        </thead>
                        <tbody id="country-breakdown">
                        </tbody>
                    </table>
                    </div>

                    <!-- Processing Issues -->
                    <div class="processing-issues-section">
                        <h3>Processing Issues</h3>
                        <div id="processing-issues"></div>
                    </div>

                    <!-- Export Buttons -->
        <div id="export-buttons" style="display: none;">
            <h3>📊 Export Reports</h3>
            <div class="export-grid">
                <button id="export-csv" class="export-btn">
                    📋 Export IOSS CSV (Basic)
                </button>
                <button id="export-ioss-compliant" class="export-btn">
                    ✅ Export IOSS Compliant CSV
                </button>
                <button id="export-detailed" class="export-btn">
                    📄 Export Detailed Report
                </button>
                <button id="export-xml" class="export-btn">
                    🗂️ Export XML
                </button>
                <button id="export-estonian-vat-xml" class="export-btn">
                    🇪🇪 Export for Estonian VAT XML Generator
                </button>
            </div>
        </div>
                </div>
            </section>
        </main>

        <footer>
            <p>IOSS Reporter v1.0 - Local Application</p>
        </footer>
    </div>

    <!-- Include Papa Parse for CSV parsing -->
    <script src="https://unpkg.com/papaparse@5.4.1/papaparse.min.js"></script>

    <!-- Debug script to test if JavaScript works at all -->
    <script>
        console.log('🧪 Inline JavaScript test');

        // Test if DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            console.log('📄 DOM loaded - inline script working');

            // Add a debug button
            const debugBtn = document.createElement('button');
            debugBtn.innerHTML = '🔧 Debug JS';
            debugBtn.className = 'btn btn-danger btn-sm';
            debugBtn.style.position = 'fixed';
            debugBtn.style.top = '10px';
            debugBtn.style.right = '10px';
            debugBtn.style.zIndex = '9999';
            debugBtn.onclick = () => {
                console.log('🔧 Debug button clicked');
                alert('Inline JavaScript works!\n\nCheck console for more details.');

                // Test file input
                const fileInput = document.getElementById('file-input');
                console.log('📁 File input found:', !!fileInput);

                if (fileInput) {
                    console.log('🧪 Attempting to trigger file input...');
                    fileInput.click();
                }
            };

            document.body.appendChild(debugBtn);
            console.log('✅ Debug button added');
        });
    </script>

    <script src="script.js"></script>
</body>
</html>
