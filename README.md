# IOSS Reporter

A local web application for converting Shopify order data to IOSS (Import One-Stop Shop) reporting format for EU VAT compliance.

## Features

- **CSV File Upload**: Drag & drop or select Shopify order export CSV files
- **IOSS Compliance**: Automatically identifies EU orders under €150 threshold
- **Multi-Shop Support**: Combine multiple Shopify reports from different shops for one company
- **VAT Calculation**: Calculates or validates VAT amounts using current EU rates
- **Export Options**: Generate CSV summaries, detailed reports, and XML files
- **Issue Detection**: Identifies data inconsistencies and compliance issues

## Quick Start

1. Open `index.html` in your web browser
2. Upload your Shopify orders CSV file
3. Enter company and IOSS information
4. Process the data and review results
5. Export reports as needed

## Supported File Format

The application expects Shopify order export CSV files with these key columns:
- Name (Order ID)
- Financial Status
- Shipping Country
- Currency
- Taxes
- Lineitem name, quantity, price
- Created at

## IOSS Compliance Features

- **EU Country Detection**: Automatically identifies orders to EU member states
- **Threshold Validation**: Flags orders over €150 that require different handling
- **VAT Rate Application**: Uses current EU VAT rates by country
- **Multiple Currency Support**: Handles USD, EUR, GBP transactions
- **Quarterly Reporting**: Generates reports for EU submission

## Export Formats

1. **CSV Summary**: Country-by-country breakdown
2. **Detailed CSV**: Individual order details
3. **XML Report**: EU-compliant IOSS return format

## Browser Compatibility

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Privacy & Security

- All processing happens locally in your browser
- No data is sent to external servers
- Files remain on your computer

## Technical Notes

- Uses Papa Parse library for CSV processing
- Responsive design for desktop and mobile
- Modern JavaScript (ES6+)
- No backend required - pure client-side application

## License

This is a local utility tool. Use at your own discretion for IOSS compliance reporting.
