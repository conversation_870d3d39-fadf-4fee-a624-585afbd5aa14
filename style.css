* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 600;
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.card {
    background: white;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid #e1e8ed;
}

.card h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.5rem;
    border-bottom: 2px solid #e1e8ed;
    padding-bottom: 10px;
}

/* Upload Area Styling */
.upload-area {
    border: 3px dashed #cbd5e0;
    border-radius: 12px;
    padding: 60px 20px;
    text-align: center;
    transition: all 0.3s ease;
    background-color: #f8fafc;
}

.upload-area:hover,
.upload-area.drag-over {
    border-color: #667eea;
    background-color: #f0f4ff;
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.upload-icon {
    font-size: 3rem;
    opacity: 0.6;
}

.upload-area p {
    color: #6b7280;
    font-size: 1.1rem;
}

/* Button Styling */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    margin: 5px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f1f5f9;
    color: #334155;
    border: 1px solid #cbd5e0;
}

.btn-secondary:hover {
    background: #e2e8f0;
    transform: translateY(-1px);
}

/* Form Styling */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* File Info */
.file-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    margin-top: 15px;
}

.file-info p {
    margin: 5px 0;
    color: #495057;
}

.loaded-files {
    margin-bottom: 15px;
}

.file-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.file-item:last-child {
    margin-bottom: 0;
}

.file-name {
    font-weight: 500;
    color: #495057;
    display: block;
    margin-bottom: 4px;
}

.file-details {
    font-size: 0.9em;
    color: #6c757d;
}

.total-summary {
    font-weight: 600;
    color: #495057;
    border-top: 1px solid #e9ecef;
    padding-top: 10px;
    margin-top: 10px;
}

.upload-note {
    font-size: 0.9em;
    color: #6c757d;
    font-style: italic;
    margin: 5px 0;
}

/* Tab Navigation Styling */
.tab-navigation {
    display: flex;
    margin-bottom: 25px;
    border-bottom: 2px solid #e1e8ed;
    gap: 10px;
}

.tab-btn {
    padding: 12px 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    color: #6b7280;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    border-radius: 8px 8px 0 0;
}

.tab-btn:hover {
    background-color: #f8fafc;
    color: #374151;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background-color: #f0f4ff;
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.tab-content h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

/* Info Box for Refunds Tab */
.info-box {
    background: linear-gradient(135deg, #e8f2ff 0%, #f0f7ff 100%);
    border: 1px solid #bfdbfe;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
}

.info-box p {
    margin-bottom: 10px;
    color: #1e40af;
}

.info-box ul {
    margin-left: 20px;
    color: #374151;
}

.info-box li {
    margin-bottom: 5px;
}

/* Refunds-specific styling */
.refunds-summary {
    font-weight: 600;
    color: #dc2626;
    border-top: 1px solid #e9ecef;
    padding-top: 10px;
    margin-top: 10px;
}

#refunds-upload-area {
    border-color: #fbbf24;
    background-color: #fffbeb;
}

#refunds-upload-area:hover,
#refunds-upload-area.drag-over {
    border-color: #f59e0b;
    background-color: #fef3c7;
}

/* Status and Processing */
.status {
    text-align: center;
    padding: 20px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.stat-item {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e2e8f0;
}

.stat-label {
    display: block;
    color: #6b7280;
    font-size: 0.9rem;
    margin-bottom: 8px;
    font-weight: 500;
}

.stat-value {
    display: block;
    color: #1f2937;
    font-size: 1.8rem;
    font-weight: 700;
}

/* Table Styling */
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

table th {
    background: #f8fafc;
    color: #374151;
    font-weight: 600;
    padding: 15px;
    text-align: left;
    border-bottom: 2px solid #e5e7eb;
}

table td {
    padding: 12px 15px;
    border-bottom: 1px solid #f3f4f6;
}

table tr:hover {
    background: #f8fafc;
}

/* Export Options */
.export-options {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

/* Issues Section */
#issues-section {
    background: #fef3cd;
    border: 1px solid #fbbf24;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

#issues-section h3 {
    color: #92400e;
    margin-bottom: 15px;
}

.issue-item {
    background: white;
    padding: 10px 15px;
    margin-bottom: 10px;
    border-radius: 6px;
    border-left: 4px solid #f59e0b;
}

.issue-item.error {
    border-left-color: #ef4444;
    background: #fef2f2;
}

.issue-item.warning {
    border-left-color: #f59e0b;
    background: #fffbeb;
}

/* Footer */
footer {
    text-align: center;
    padding: 30px 0;
    color: #6b7280;
    border-top: 1px solid #e5e7eb;
    margin-top: 40px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    header h1 {
        font-size: 2rem;
    }

    .card {
        padding: 20px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .export-options {
        flex-direction: column;
    }

    .export-options .btn {
        margin: 5px 0;
    }
}

/* Success indicators */
.success {
    color: #059669;
    background: #ecfdf5;
    border: 1px solid #a7f3d0;
    padding: 10px 15px;
    border-radius: 6px;
    margin: 10px 0;
}

.error {
    color: #dc2626;
    background: #fef2f2;
    border: 1px solid #fca5a5;
    padding: 10px 15px;
    border-radius: 6px;
    margin: 10px 0;
}

/* Progress indicators */
.progress-step {
    display: flex;
    align-items: center;
    margin: 10px 0;
}

.progress-step.completed {
    color: #059669;
}

.progress-step.active {
    color: #2563eb;
    font-weight: 600;
}

.progress-step::before {
    content: '○';
    margin-right: 10px;
    font-size: 1.2rem;
}

.progress-step.completed::before {
    content: '✓';
    color: #059669;
}

.progress-step.active::before {
    content: '◐';
    color: #2563eb;
}

/* Refunds Processing Specific Styles */
.process-section {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid #bae6fd;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
    text-align: center;
}

.process-section h4 {
    color: #0c4a6e;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.process-section p {
    color: #0369a1;
    margin-bottom: 15px;
}

/* Export section styling */
.export-section {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    margin-top: 25px;
}

.export-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 10px;
    margin-top: 15px;
}

.export-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-align: center;
}

.export-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* VAT Difference Styling */
.vat-diff {
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 4px;
}

.vat-diff.positive {
    color: #d73527;
    background-color: #ffeaea;
}

.vat-diff.negative {
    color: #28a745;
    background-color: #eafaf1;
}

.vat-diff.neutral {
    color: #6c757d;
    background-color: #f8f9fa;
}

.breakdown-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.breakdown-table th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #374151;
    font-weight: 600;
    padding: 15px;
    text-align: left;
    border-bottom: 2px solid #e5e7eb;
}

.breakdown-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #f3f4f6;
    color: #374151;
}

.breakdown-table tr:hover {
    background: #f8fafc;
}

.breakdown-description {
    color: #6b7280;
    font-style: italic;
    margin-bottom: 10px;
}
